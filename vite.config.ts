import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
    AutoImport({
      // 自动按需导入常用 API
      imports: ['vue', 'vue-router', 'pinia', 'vue-i18n'],
      dts: 'src/types/auto-imports.d.ts',
      vueTemplate: true,
      resolvers: [ElementPlusResolver()],
      dirs: ['src/stores', 'src/composables'],
      eslintrc: {
        enabled: false,
      },
    }),
    Components({
      // 自动注册组件（包含 Element Plus）
      dts: 'src/types/components.d.ts',
      resolvers: [
        ElementPlusResolver({
          importStyle: 'sass',
        }),
      ],
      directoryAsNamespace: false,
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  server: {
    port: 3000,
    open: false,
  },
})
