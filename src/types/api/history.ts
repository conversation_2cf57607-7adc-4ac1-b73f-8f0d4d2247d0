export interface CommonItem {
  id: number
  orderid: string
  time: string
  status: number
  status_str: string
}

export interface DepositHistoryItem extends CommonItem {
  coin: string
  title: string
  memo?: string
}

export interface WithdrawHistoryItem extends CommonItem {
  coin: string
  memo?: string
}

export interface BetHistoryItem extends CommonItem {
  bet: number
  wincoin: number | string
  gameid: number
  title: string
}

export interface BonusHistoryItem extends CommonItem {
  coin: string
  title: string
}

export interface HistoryData {
  deplist?: Record<string, DepositHistoryItem[]>
  drawlist?: Record<string, WithdrawHistoryItem[]>
  betlist?: Record<string, BetHistoryItem[]>
  bonuslist?: Record<string, BonusHistoryItem[]>
  banklist?: unknown[]
  show?: number
  url?: string
}
