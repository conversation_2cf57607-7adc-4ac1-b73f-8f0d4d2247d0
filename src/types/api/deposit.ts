export interface BalanceData {
  user: UserData
  coins: number[]
  channels: DepositData[]
  memo: string
  popmsg?: string
  customer?: unknown[]
  url?: string
}

// 用户信息
export interface UserData {
  uid: number
  coin: number
  dcoin: number
  ecoin?: number
  bonus?: number
  totalcoin?: number
  kyc?: number
  svip?: number
  ispayer?: number
  nodislabelid?: number
}

// 充值渠道
export interface DepositData {
  id: number
  title: string
  icon?: string
  subtype?: string
  mincoin: number
  maxcoin: number
  disrate: string
  discoin: number
  type: number
  pages: DepositItem[]
}

// 充值渠道页面
export interface DepositItem {
  id: number
  title: string
  type: number
  banktype: number
  mincoin: number
  maxcoin: number
  disrate: string
  discoin: number
  rate: number | string
}

export interface DepositResult {
  chain?: string
  token?: string
  order_id?: string
  trade_id?: string
  address?: string
  amount?: number
  actual_amount?: number | string
  expiration_time?: number
  payment_url?: string
}

export interface DepositStatus {
  trade_id: string
  status: number
  trade_hash: string
  return_url: string
}
