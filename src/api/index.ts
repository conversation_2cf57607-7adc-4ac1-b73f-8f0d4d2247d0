import { http } from './request'
import type { BalanceData } from '@/types/api/deposit'
import type { HistoryData } from '@/types/api/history'
import type { WithdrawData, UserBanksData } from '@/types/api/withdraw'
import type { DepositResult, DepositStatus } from '@/types/api/deposit'
// 类型按需引用，避免构建时路径解析问题

// 获取用户余额
export const userBalance = (amount: number, cat = 2, paypop = 0) =>
  http.get<BalanceData>(`/user/balance?coin=${amount}&cat=${cat}&paypop=${paypop}`)

// 交易记录
export const userHistory = (num = 1, size = 10) =>
  http.get<HistoryData>(`/user/history?num=${num}&size=${size}`)

// 银行卡列表
export const userBanks = () => http.get<UserBanksData>('/user/banks')

// 创建订单
export const orderPay = (payload: { amount: number; id: number }) =>
  http.post<DepositResult>('/order/pay', payload)

// 检查订单
export const orderCheck = (payload: { orderid: string; tradeid: string }) =>
  http.get<DepositStatus>('/order/check', { params: payload })

// 提现初始化信息（限额、可提现余额、提示等）
export const withdrawData = (drawpop = 0) =>
  http.get<WithdrawData>(`/draw/index?drawpop=${drawpop}`)

export const withdrawOrder = (payload: { dcoin: number; bankid: number }) =>
  http.post<unknown>('/draw/order', payload)

// 添加银行卡（提现账户）
export const addBankAccount = (payload: {
  account: string
  username: string
  bankname?: string
  ifsc: string
  email?: string
  pic?: string
  state?: string
}) => http.post<unknown>('/user/addcard', { cat: 'bank', ...payload })

// 添加 USDT 提现账户
export const addUsdtAccount = (payload: { addr: string; type?: number }) =>
  http.post<unknown>('/user/addcard', { cat: 'usdt', type: 1, ...payload })
