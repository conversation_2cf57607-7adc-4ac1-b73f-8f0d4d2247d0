import axios, {
  type AxiosError,
  type AxiosInstance,
  type AxiosResponse,
  type AxiosRequestConfig,
  type InternalAxiosRequestConfig,
} from 'axios'
import { ElMessage } from 'element-plus'

// 接口响应格式
interface Api<T> {
  code: number
  msg: string
  data: T
}

type Cfg<D = unknown> = AxiosRequestConfig<D>
type Res<T> = AxiosResponse<Api<T>>
type Err<T = unknown> = AxiosError<T>

const request: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
  headers: { Accept: 'application/json' },
})

request.interceptors.request.use((config: InternalAxiosRequestConfig) => {
  const uid = localStorage.getItem('uid') || ''
  const token = localStorage.getItem('token') || ''
  if (config.params instanceof URLSearchParams) {
    config.params.set('uid', uid)
    config.params.set('token', token)
  } else {
    config.params = { ...(config.params ?? {}), uid, token }
  }
  return config
})

request.interceptors.response.use(
  <T>(response: Res<T>) => {
    const { code, data, msg } = response.data
    if (code === 0) return data
    ElMessage.error(msg || 'Request failed')
    return Promise.reject(msg || 'Request failed')
  },
  (error: Err) => {
    const status = error.response?.status
    const message = error.message || 'Network Error'
    if (status === 401) {
      ElMessage.error('Unauthorized')
    } else if (status) {
      ElMessage.error(`${status}: ${message}`)
    } else {
      ElMessage.error(message)
    }
    return Promise.reject(error)
  },
)

export const http = {
  // 响应拦截器已将 AxiosResponse 解包为 data，这里直接返回 Promise<T>
  get: <T, D = unknown>(url: string, config?: Cfg<D>) =>
    request.get<T, Res<T>, D>(url, config) as unknown as Promise<T>,
  put: <T, D = unknown>(url: string, data?: D, config?: Cfg<D>) =>
    request.put<T, Res<T>, D>(url, data, config) as unknown as Promise<T>,
  post: <T, D = unknown>(url: string, data?: D, config?: Cfg<D>) =>
    request.post<T, Res<T>, D>(url, data, config) as unknown as Promise<T>,
  patch: <T, D = unknown>(url: string, data?: D, config?: Cfg<D>) =>
    request.patch<T, Res<T>, D>(url, data, config) as unknown as Promise<T>,
  delete: <T, D = unknown>(url: string, config?: Cfg<D>) =>
    request.delete<T, Res<T>, D>(url, config) as unknown as Promise<T>,
}

export default request
