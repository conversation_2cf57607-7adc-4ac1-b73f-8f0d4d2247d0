import { defineStore } from 'pinia'
import { ref } from 'vue'
import { type DepositResult, type DepositStatus } from '@/types/api/deposit'
import { useI18n } from 'vue-i18n'
import { ElMessageBox } from 'element-plus'
import { orderCheck } from '@/api'

export const useCashierStore = defineStore('cashier', () => {
  const { t } = useI18n()
  const state = ref<DepositResult>({})
  const pollTimer = ref<number | null>(null)
  const isPolling = ref(false)
  const waitingOpen = ref(false)

  function setup(value: DepositResult) {
    state.value = value
  }

  // --- 轮询 ---
  function scheduleNext(delayMs = 5000) {
    if (!isPolling.value) return
    if (pollTimer.value) clearTimeout(pollTimer.value)
    pollTimer.value = window.setTimeout(pollOnce, delayMs)
  }

  async function pollOnce() {
    if (!isPolling.value) return
    if (!state.value.order_id || !state.value.trade_id) {
      stop()
      return
    }
    try {
      const data = await orderCheck({
        orderid: state.value.order_id,
        tradeid: state.value.trade_id,
      })
      handleStatus(data)
    } catch (err) {
      // 网络/服务端异常：稍后重试
      scheduleNext(5000)
    }
  }

  function handleStatus(data: DepositStatus) {
    switch (data.status) {
      case 1: // 等待支付
        scheduleNext(5000)
        break
      case 2: // 成功
        onSuccess(data)
        break
      case 3: // 超时
        onExpired()
        break
      case 5: // 等待确认
        showWaitingOverlay()
        scheduleNext(5000)
        break
      default:
        scheduleNext(5000)
        break
    }
  }

  function startPolling() {
    if (isPolling.value) return
    isPolling.value = true
    pollOnce()
  }

  function stopPolling() {
    isPolling.value = false
    if (pollTimer.value) {
      clearTimeout(pollTimer.value)
      pollTimer.value = null
    }
  }

  // --- 弹窗逻辑 ---
  function onSuccess(data: DepositStatus) {
    hideWaitingOverlay()
    showSuccessOverlay(data)
    stop()
  }

  function onExpired() {
    hideWaitingOverlay()
    ElMessageBox.alert(
      `<div style="text-align:center">
        <div style="font-size:34px">⏰</div>
        <div style="font-weight:700;color:#d33;margin:6px 0">${t('cashier.expiredTitle')}</div>
        <div style="color:#666;">${t('cashier.expiredDesc')}</div>
      </div>`,
      '',
      {
        center: true,
        showClose: false,
        dangerouslyUseHTMLString: true,
        customClass: 'cashier-expired-box',
        confirmButtonText: t('cashier.expiredBack'),
        callback: () => {
          window.location.href = 'openurl://close'
        },
      },
    )
  }

  function showWaitingOverlay() {
    if (waitingOpen.value) return
    waitingOpen.value = true
    ElMessageBox.alert(
      `<div style="text-align:center">
        <div style="font-size:48px;margin-bottom:10px;">⏳</div>
        <div style="font-weight:700;color:#4f46e5;margin:6px 0;">${t('cashier.waitingTitle')}</div>
        <div style="color:#666;">${t('cashier.waitingDesc')}</div>
        <div style="color:#999;margin-top:12px;font-size:12px;">${t('cashier.waitingHint')}</div>
      </div>`,
      '',
      {
        center: true,
        showClose: false,
        dangerouslyUseHTMLString: true,
        customClass: 'cashier-waiting-box',
        showConfirmButton: false,
        closeOnClickModal: false,
        closeOnPressEscape: false,
      },
    )
      .then(() => {})
      .catch(() => {})
      .finally(() => {
        waitingOpen.value = false
      })
  }

  function hideWaitingOverlay() {
    if (waitingOpen.value) {
      ElMessageBox.close()
      waitingOpen.value = false
    }
  }

  function showSuccessOverlay(data: DepositStatus) {
    ElMessageBox.alert(
      `<div style="text-align:center">
        <div style="font-size:48px;margin-bottom:20px;">✅</div>
        <h3 style="color:#48bb78;margin-bottom:15px;">${t('cashier.successTitle')}</h3>
        <p style="color:#666;margin-bottom:20px;line-height:1.5;">${t('cashier.successDesc')}<br>
          <code style="background:#e6f0fa;color:#2563eb;padding:4px 8px;border-radius:4px;font-size:12px;word-break:break-all;">${data.trade_hash}</code>
        </p>
      </div>`,
      '',
      {
        center: true,
        showClose: false,
        dangerouslyUseHTMLString: true,
        customClass: 'cashier-success-box',
        showConfirmButton: false,
      },
    )
  }

  return {
    setup,
    state,
    startPolling,
    stopPolling,
    onExpired,
    onSuccess,
  }
})
