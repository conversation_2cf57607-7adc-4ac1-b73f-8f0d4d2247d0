import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { userBalance, orderPay } from '@/api'
import type { BalanceData, DepositData, DepositItem, DepositResult } from '@/types/api/deposit'
import { parseStoredAmount } from '@/utils/format'

export const useDepositStore = defineStore('deposit', () => {
  const data = ref<BalanceData | null>(null)
  const amount = ref<number>(parseStoredAmount(localStorage.getItem('amount')))
  const selectedMethodId = ref<number | null>(null)
  const selectedPageId = ref<number | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  const methods = computed<DepositData[]>(() => data.value?.channels ?? [])
  const selectedMethod = computed<DepositData | null>(
    () => methods.value.find((m) => m.id === selectedMethodId.value) ?? null,
  )

  const pages = computed<DepositItem[]>(() => selectedMethod.value?.pages ?? [])
  const selectedPage = computed<DepositItem | null>(
    () => pages.value.find((p) => p.id === selectedPageId.value) ?? null,
  )

  async function refresh() {
    loading.value = true
    error.value = null
    try {
      data.value = (await userBalance(amount.value)) as unknown as BalanceData
      const channels = data.value?.channels ?? []
      if (!selectedMethodId.value) selectedMethodId.value = channels[0]?.id ?? null
      if (!selectedPageId.value) {
        const first = channels.find((m) => m.id === selectedMethodId.value)?.pages?.[0]
        selectedPageId.value = first?.id ?? null
      }
    } catch (e) {
      error.value = (e as Error)?.message || String(e) || 'Network Error'
    } finally {
      loading.value = false
    }
  }

  function selectMethod(id: number) {
    selectedMethodId.value = id
    const method = methods.value.find((m) => m.id === id)
    selectedPageId.value = method?.pages?.[0]?.id ?? null
  }

  function selectPage(id: number) {
    selectedPageId.value = id
  }

  async function submit(): Promise<DepositResult> {
    if (!selectedMethodId.value) throw new Error('请选择支付方式')
    const res = await orderPay({ amount: amount.value, id: selectedMethodId.value })
    return res
  }

  watch(amount, (n) => localStorage.setItem('amount', String(n)))

  return {
    data,
    amount,
    methods,
    pages,
    selectedMethodId,
    selectedPageId,
    selectedMethod,
    selectedPage,
    loading,
    error,
    refresh,
    selectMethod,
    selectPage,
    submit,
  }
})
