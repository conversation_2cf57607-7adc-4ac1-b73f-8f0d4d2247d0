import { defineStore } from 'pinia'

export interface AppBarState {
  showBack: boolean | null
  titleKey: string | null
  supportUrl: string | null
}

export const useAppBarStore = defineStore('appbar', {
  state: (): AppBarState => ({
    showBack: null,
    titleKey: null,
    supportUrl: null,
  }),
  actions: {
    set(partial: Partial<AppBarState>) {
      if (partial.showBack !== undefined) this.showBack = partial.showBack
      if (partial.titleKey !== undefined) this.titleKey = partial.titleKey
      if (partial.supportUrl !== undefined) this.supportUrl = partial.supportUrl
    },
    reset(defaults: { titleKey: string; showBack: boolean }) {
      this.showBack = defaults.showBack
      this.titleKey = defaults.titleKey
      this.supportUrl = null
    },
  },
})
