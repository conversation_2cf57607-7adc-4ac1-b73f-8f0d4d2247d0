import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { withdrawData, userBanks, withdrawOrder } from '@/api'
import type { WithdrawData, UserBanksData } from '@/types/api/withdraw'

export const useWithdrawStore = defineStore('withdraw', () => {
  const data = ref<WithdrawData | null>(null)
  const amount = ref<number>(0)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const bankList = ref<UserBanksData['banks']>([])
  const selectedBankId = ref<number | null>(null)
  const isMock = ref(false)

  const canSubmit = computed(
    () =>
      amount.value >= (data.value?.mincoin ?? 0) &&
      amount.value <= (data.value?.maxcoin ?? 0) &&
      !!selectedBankId.value,
  )

  async function fetchInit() {
    loading.value = true
    error.value = null
    try {
      const [initRes, banksRes] = await Promise.all([withdrawData(), userBanks()])
      data.value = initRes
      bankList.value = banksRes.banks || []
      const checked = bankList.value.find((b) => b.checked)
      selectedBankId.value = checked?.id ?? bankList.value[0]?.id ?? null
    } catch (e) {
      error.value = (e as Error)?.message || String(e)
    } finally {
      loading.value = false
    }
  }

  function selectBank(id: number) {
    selectedBankId.value = id
  }

  async function submit() {
    if (!selectedBankId.value) throw new Error('请选择银行卡')
    if (!canSubmit.value) throw new Error('金额不在可提范围内')
    if (isMock.value) {
      return Promise.resolve()
    }
    await withdrawOrder({ dcoin: amount.value, bankid: selectedBankId.value })
  }

  return {
    data,
    loading,
    error,
    amount,
    banks: bankList,
    selectedBankId,
    canSubmit,
    fetchInit,
    selectBank,
    submit,
  }
})
