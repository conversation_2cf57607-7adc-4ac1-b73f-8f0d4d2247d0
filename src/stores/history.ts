import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { userHistory } from '@/api'
import type {
  HistoryData,
  DepositHistoryItem,
  WithdrawHistoryItem,
  BetHistoryItem,
  BonusHistoryItem,
} from '@/types/api/history'
import { getLegendKey } from '@/utils/status'

export type HistoryTab = 'deposit' | 'withdraw' | 'bet' | 'bonus'
export type LegendKey =
  | 'success'
  | 'inProcess'
  | 'failed'
  | 'refund'
  | 'win'
  | 'loss'
  | 'transferIn'
  | 'transferOut'

export type HistoryItem =
  | DepositHistoryItem
  | WithdrawHistoryItem
  | BetHistoryItem
  | BonusHistoryItem

type HistoryMap = Record<string, HistoryItem[]>

export const useHistoryStore = defineStore('history', () => {
  const data = ref<HistoryData | null>(null)
  const active = ref<HistoryTab>('deposit')
  const pageNum = ref(1)
  const pageSize = ref(10)
  const loading = ref(false)
  const hasMore = ref(true)
  const filters = ref<LegendKey[]>([])

  const sourceMap = computed(() => ({
    deposit: data.value?.deplist as unknown as HistoryMap | undefined,
    withdraw: data.value?.drawlist as unknown as HistoryMap | undefined,
    bet: data.value?.betlist as unknown as HistoryMap | undefined,
    bonus: data.value?.bonuslist as unknown as HistoryMap | undefined,
  }))

  const isDW = computed(() => active.value === 'deposit' || active.value === 'withdraw')
  const isBet = computed(() => active.value === 'bet')

  const legendKeys = computed<LegendKey[]>(() => {
    const map: Record<HistoryTab, LegendKey[]> = {
      deposit: ['success', 'inProcess', 'failed'],
      withdraw: ['success', 'inProcess', 'failed'],
      bet: ['win', 'loss'],
      bonus: ['transferIn', 'transferOut'],
    }
    return map[active.value]
  })

  function labelKeyOf(item: HistoryItem): LegendKey {
    return getLegendKey(active.value, item)
  }

  function matchFilter(item: HistoryItem): boolean {
    if (filters.value.length === 0) return true
    return filters.value.includes(labelKeyOf(item))
  }

  const groupedMap = computed<HistoryMap>(() => {
    const src = (sourceMap.value as Record<string, HistoryMap | undefined>)[active.value]
    if (!src) return {}
    const result: HistoryMap = {}
    Object.entries(src).forEach(([date, arr]) => {
      const filtered = (arr as HistoryItem[]).filter((it) => matchFilter(it))
      if (filtered.length) result[date] = filtered
    })
    return result
  })

  const hasData = computed(() => Object.keys(groupedMap.value).length > 0)

  function toggleFilter(key: LegendKey) {
    const idx = filters.value.indexOf(key)
    if (idx >= 0) filters.value.splice(idx, 1)
    else filters.value.push(key)
  }

  async function fetchData(reset = false) {
    loading.value = true
    try {
      const res = await userHistory(pageNum.value, pageSize.value)
      if (reset || !data.value) {
        data.value = res
        hasMore.value = hasAny(res)
      } else {
        hasMore.value = mergeHistory(data.value, res)
      }
    } finally {
      loading.value = false
    }
  }

  function loadMore() {
    if (loading.value || !hasMore.value) return
    pageNum.value += 1
    fetchData()
  }

  function mergeHistory(target: HistoryData, incoming: HistoryData): boolean {
    let added = false
    ;(['deplist', 'drawlist', 'betlist', 'bonuslist'] as const).forEach((key) => {
      const src = (incoming as unknown as Record<string, HistoryMap | undefined>)[key]
      if (!src) return
      const store = target as unknown as Record<string, HistoryMap | undefined>
      const dst = (store[key] = store[key] || {}) as HistoryMap
      Object.entries(src).forEach(([date, arr]) => {
        if (!dst[date]) dst[date] = []
        const before = dst[date].length
        dst[date] = dst[date].concat(arr as HistoryItem[])
        if (dst[date].length > before) added = true
      })
    })
    return added
  }

  function hasAny(payload: HistoryData): boolean {
    return Boolean(
      (payload.deplist && Object.keys(payload.deplist).length) ||
        (payload.drawlist && Object.keys(payload.drawlist).length) ||
        (payload.betlist && Object.keys(payload.betlist).length) ||
        (payload.bonuslist && Object.keys(payload.bonuslist).length),
    )
  }

  function resetForTab() {
    filters.value = []
    pageNum.value = 1
    hasMore.value = true
  }

  return {
    // state
    active,
    pageNum,
    pageSize,
    loading,
    data,
    hasMore,
    filters,
    // getters
    legendKeys,
    groupedMap,
    hasData,
    isDW,
    isBet,
    // actions
    toggleFilter,
    fetchData,
    loadMore,
    resetForTab,
    // utils
    labelKeyOf,
  }
})
