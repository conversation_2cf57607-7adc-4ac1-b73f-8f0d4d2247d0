<script setup lang="ts">
import AppHeader from '@/components/AppHeader.vue'
import { useAppBarStore } from '@/stores/appbar'
import { ElConfigProvider } from 'element-plus'
import { useViewport } from '@/composables/useViewport'

const store = useAppBarStore()

// 初始化视觉控制系统
const { componentSize } = useViewport({
  baseFontSize: {
    mobile: 17, // 移动端使用更大的字体
    tablet: 16,
    desktop: 14,
  },
})
</script>

<template>
  <el-config-provider :size="componentSize" :button="{ autoInsertSpace: true }">
    <div class="app-layout">
      <AppHeader
        :title-key="store.titleKey || 'app.name'"
        :show-back="store.showBack ?? false"
        :support-url="store.supportUrl"
        variant="sticky"
      />
      <div class="app-main">
        <main>
          <router-view />
        </main>
      </div>
    </div>
  </el-config-provider>
</template>

<style scoped lang="scss">
.app-layout {
  min-height: 100vh;
  background: var(--el-bg-color);
}
.app-main {
  max-width: 100%;
}
</style>
