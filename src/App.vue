<script setup lang="ts">
import AppHeader from '@/components/AppHeader.vue'
import { useAppBarStore } from '@/stores/appbar'
import { defineComponent } from 'vue'
import { ElConfigProvider } from 'element-plus'

export default defineComponent({
  components: {
    ElConfigProvider,
  },
  setup() {
    return {
      zIndex: 3000,
      size: 'small',
    }
  },
})
const store = useAppBarStore()
</script>

<template>
  <el-config-provider :button="{ autoInsertSpace: true }">
    <div class="app-layout">
      <AppHeader
        :title-key="store.titleKey || 'app.name'"
        :show-back="store.showBack ?? false"
        :support-url="store.supportUrl"
        variant="sticky"
      />
      <div class="app-main">
        <main>
          <router-view />
        </main>
      </div>
    </div>
  </el-config-provider>
</template>

<style scoped lang="scss">
.app-layout {
  min-height: 100vh;
  background: var(--el-bg-color);
}
.app-main {
  max-width: 100%;
}
</style>
