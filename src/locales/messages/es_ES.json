{"app": {"name": "Payment"}, "page": {"deposit": "Recargar", "histories": "Transacciones", "cashier": "Caja", "history": "Transacciones", "withdraw": "<PERSON><PERSON><PERSON>"}, "common": {"tips": "Consejos", "customerService": "Soporte", "submit": "Enviar", "cancel": "<PERSON><PERSON><PERSON>", "added": "<PERSON>g<PERSON>gado correctamente", "paste": "<PERSON><PERSON><PERSON>", "more": "Más opcional", "less": "<PERSON><PERSON><PERSON>"}, "cashier": {"title": "Pago USDT", "network": "Utilice la red {chain} para la transferencia", "countdown": "Cuenta atrás de pago", "scanTip": "Complete el pago dentro de la cuenta atrás, de lo contrario el pedido será cancelado", "addressLabel": "Dirección de recepción ({chain})", "copyAddress": "<PERSON><PERSON><PERSON>", "amount": "Importe de la transferencia", "orderNo": "Pedido del comerciante", "tradeId": "ID de transacción", "noticeTitle": "Aviso de acreditación", "poweredBy": "Con tecnología de BEpusdt", "unitMin": "min", "unitSec": "seg", "currency": "USDT", "expiredTitle": "Tiempo de pago expirado", "expiredDesc": "<PERSON> sentimo<PERSON>, el tiempo de pago ha expirado. Inicie el pago nuevamente o contacte al soporte.", "expiredBack": "Volver al comerciante", "tips": ["Debe transferir mediante la red TRC20. ¡No use TRX!", "Retrollamada automática tras confirmaciones en cadena", "El importe recibido debe coincidir con el importe a pagar", "<PERSON> hay algún problema, contacte al soporte"], "copied": "Copiado", "successTitle": "Pago realizado con éxito!", "successDesc": "Su pago ha sido confirmado, hash de la transacción:", "waitingTitle": "En espera de confirmación en cadena", "waitingDesc": "Pa<PERSON>ado, en espera de confirmación en cadena...", "waitingHint": "Por favor, espere, el sistema verificará el estado de la transacción", "waitingEta": "Tiempo estimado de confirmación: 1-3 minutos"}, "deposit": {"amount": "Importe de recarga", "currentBalance": "<PERSON><PERSON>", "paymentMethod": "Método de pago", "paymentChannel": "Canal de pago", "limitHint": "Rango permitido por operación: {min} ~ {max}", "cashBalance": "Saldo en efectivo", "cashBonus": "<PERSON><PERSON>carga", "submit": "<PERSON><PERSON><PERSON> ahora", "submitAmount": "Recargar {amount} ahora", "outOfRange": "Ingrese un importe entre {min} y {max}", "noMethods": "No hay métodos de pago disponibles", "selectamount": "Importe de recarga"}, "history": {"tab": {"deposit": "Recargar", "withdraw": "<PERSON><PERSON><PERSON>", "bet": "Apuesta", "bonus": "<PERSON><PERSON>"}, "loadMore": "<PERSON>gar más", "copy": "Copiar", "copied": "Copiado", "needHelp": "¿Necesita ayuda? Contáctenos.", "empty": "Sin registros", "legend": {"success": "Éxito", "inProcess": "En proceso", "failed": "Fallido", "refund": "Reembolso", "win": "G<PERSON><PERSON>", "loss": "<PERSON><PERSON>", "transferIn": "Transferir a", "transferOut": "Transferir desde"}, "tip": {"deposit": "Las recargas suelen acreditarse en minutos", "withdraw": "Los retiros suelen procesarse en minutos", "bet": "Todas las apuestas se muestran aquí", "bonus": "Todos los bonos se muestran aquí"}, "betAmount": "Importe de la apuesta", "win": "G<PERSON><PERSON>"}, "withdraw": {"balance": "<PERSON><PERSON> retirable", "amount": "Importe de retiro", "limit": "Rango permitido por operación: {min} ~ {max}", "submit": "<PERSON><PERSON><PERSON>", "addWithdrawalAccount": "Agregar nueva cuenta de retiro", "method": {"bank": "Tarjeta bancaria", "usdt": "USDT"}, "form": {"bank": {"account": "Número de cuenta bancaria", "ifsc": "Código IFSC", "branch": "Sucursal/Nombre", "bankname": "Nombre del banco (opcional)", "email": "Correo electrónico (opcional)", "state": "Estado/Provincia (opcional)"}, "usdt": {"addr": "Dirección USDT", "network": "Tipo de red"}}, "hint": {"account": "Complete una cuenta a su nombre", "ifsc": "IFSC se convertirá a mayúsculas y se eliminarán los espacios", "erc20": "Asegúrese de que la dirección sea de la red ERC20", "btc": "Asegúrese de que la dirección sea de la red BTC"}}}