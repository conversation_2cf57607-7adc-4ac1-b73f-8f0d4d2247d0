{"app": {"name": "Payment"}, "page": {"deposit": "<PERSON><PERSON><PERSON><PERSON>", "histories": "Transactions", "cashier": "Cashier", "history": "Transactions", "withdraw": "Withdraw"}, "common": {"tips": "Tips", "customerService": "Support", "submit": "Submit", "cancel": "Cancel", "added": "Added successfully", "paste": "Paste", "more": "More optional", "less": "Collapse"}, "cashier": {"title": "USDT Payment", "network": "Please use the {chain} network for transfer", "countdown": "Payment countdown", "scanTip": "Please complete the payment within the countdown, otherwise the order will be canceled", "addressLabel": "Receiving address ({chain})", "copyAddress": "Copy address", "amount": "Transfer amount", "orderNo": "Merchant order", "tradeId": "Transaction ID", "noticeTitle": "Credit notice", "poweredBy": "Powered by BEpusdt", "unitMin": "min", "unitSec": "sec", "currency": "USDT", "expiredTitle": "Payment time expired", "expiredDesc": "Sorry, the payment time has expired. Please start the payment again or contact support.", "expiredBack": "Back to merchant", "tips": ["You must transfer via the TRC20 network. Do not use TRX!", "Automatic callback after on-chain confirmations", "The actual received amount must match the payable amount", "If there are any issues, please contact support"], "copied": "<PERSON>pied", "successTitle": "Payment Successful!", "successDesc": "Your payment has been confirmed. Tx Hash:", "waitingTitle": "Waiting for network confirmations", "waitingDesc": "Payment detected. Confirmations pending on blockchain...", "waitingHint": "Please wait patiently. The system will automatically detect the status.", "waitingEta": "Estimated time: 1-3 minutes"}, "deposit": {"amount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "currentBalance": "Current Balance", "paymentMethod": "Payment Method", "paymentChannel": "Payment Channel", "limitHint": "Allowed range per transaction: {min} ~ {max}", "cashBalance": "Cash Balance", "cashBonus": "Deposit Bonus", "submit": "Deposit Now", "submitAmount": "Deposit {amount} Now", "outOfRange": "Please enter an amount between {min} and {max}", "noMethods": "No available payment methods", "selectamount": "Deposit amount"}, "history": {"tab": {"deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "Withdraw", "bet": "Bet", "bonus": "Bonus"}, "loadMore": "Load more", "copy": "Copy", "copied": "<PERSON>pied", "needHelp": "Need help? Please contact us.", "empty": "No records", "legend": {"success": "Success", "inProcess": "In-Process", "failed": "Failed", "refund": "Refund", "win": "Win", "loss": "Loss", "transferIn": "Transfer In", "transferOut": "Transfer Out"}, "tip": {"deposit": "Deposits are usually credited within minutes", "withdraw": "Withdrawals are usually processed within minutes", "bet": "All bet records are shown here", "bonus": "All bonus records are shown here"}, "betAmount": "Bet Amount", "win": "Win"}, "withdraw": {"balance": "Withdrawable Balance", "amount": "Withdraw Amount", "limit": "Allowed range per transaction: {min} ~ {max}", "submit": "Withdraw Now", "addWithdrawalAccount": "Add <PERSON> Withdrawal Account", "method": {"bank": "Bank Card", "usdt": "USDT"}, "form": {"bank": {"account": "Bank Account Number", "ifsc": "IFSC Code", "branch": "Branch/Name", "bankname": "Bank Name (optional)", "email": "Email (optional)", "state": "State/Province (optional)"}, "usdt": {"addr": "USDT Address", "network": "Network Type"}}, "hint": {"account": "Please fill in a payee account under your name", "ifsc": "IFSC will be uppercased and spaces removed", "erc20": "Please ensure the address is on the ERC20 network", "btc": "Please ensure the address is on the BTC network"}}}