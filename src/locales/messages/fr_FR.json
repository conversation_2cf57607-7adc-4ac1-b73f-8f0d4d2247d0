{"app": {"name": "Payment"}, "page": {"deposit": "Recharger", "histories": "Transactions", "cashier": "Caisse", "history": "Transactions", "withdraw": "Retrait"}, "common": {"tips": "Conseils", "customerService": "Support", "submit": "Envoyer", "cancel": "Annuler", "added": "Ajouté avec succès", "paste": "<PERSON><PERSON>", "more": "Plus d'options", "less": "<PERSON><PERSON><PERSON><PERSON>"}, "cashier": {"title": "Paiement USDT", "network": "Veuillez utiliser le réseau {chain} pour le virement", "countdown": "Compte à rebours de paiement", "scanTip": "<PERSON><PERSON><PERSON><PERSON> effectuer le paiement avant la fin du compte à rebours, sinon la commande sera annulée", "addressLabel": "<PERSON><PERSON><PERSON> de réception ({chain})", "copyAddress": "Co<PERSON>r l'adresse", "amount": "Montant du virement", "orderNo": "Commande commerçant", "tradeId": "ID de transaction", "noticeTitle": "<PERSON><PERSON>", "poweredBy": "Propulsé par BEpusdt", "unitMin": "min", "unitSec": "s", "currency": "USDT", "expiredTitle": "<PERSON><PERSON><PERSON> de paiement expiré", "expiredDesc": "<PERSON><PERSON><PERSON><PERSON>, le délai de paiement a expiré. Veuillez relancer le paiement ou contacter le support.", "expiredBack": "Retour au commerçant", "tips": ["<PERSON><PERSON> <PERSON><PERSON> transférer via le réseau TRC20. N'utilisez pas TRX !", "Rappel automatique après confirmations on-chain", "Le montant reçu doit correspondre au montant dû", "En cas de problème, contactez le support"], "copied": "<PERSON><PERSON><PERSON>", "successTitle": "Paiement ré<PERSON> !", "successDesc": "Votre paiement a été confirmé, hash de la transaction :", "waitingTitle": "En attente de confirmation en chaîne", "waitingDesc": "Paiement détecté, en attente de confirmation en chaîne...", "waitingHint": "<PERSON><PERSON><PERSON><PERSON> patient<PERSON>, le système vérifiera l'état de la transaction", "waitingEta": "Temps estimé de confirmation : 1-3 minutes"}, "deposit": {"amount": "<PERSON>ant de la recharge", "currentBalance": "Solde actuel", "paymentMethod": "Méthode de paiement", "paymentChannel": "Canal de paiement", "limitHint": "Plage autorisée par opération : {min} ~ {max}", "cashBalance": "Solde en espèces", "cashBonus": "Bonus de recharge", "submit": "Recharger maintenant", "submitAmount": "Recharger {amount} maintenant", "outOfRange": "Veuillez saisir un montant entre {min} et {max}", "noMethods": "Aucune méthode de paiement disponible", "selectamount": "<PERSON>ant de la recharge"}, "history": {"tab": {"deposit": "Recharger", "withdraw": "Retrait", "bet": "<PERSON><PERSON>", "bonus": "Bonus"}, "loadMore": "Charger plus", "copy": "<PERSON><PERSON><PERSON>", "copied": "<PERSON><PERSON><PERSON>", "needHelp": "Besoin d'aide ? <PERSON>ez-nous.", "empty": "Aucun enregistrement", "legend": {"success": "<PERSON><PERSON><PERSON><PERSON>", "inProcess": "En cours", "failed": "Échec", "refund": "Remboursement", "win": "<PERSON><PERSON><PERSON><PERSON>", "loss": "Perdu", "transferIn": "Virement entrant", "transferOut": "Virement sortant"}, "tip": {"deposit": "Les recharges sont généralement créditées en quelques minutes", "withdraw": "Les retraits sont généralement traités en quelques minutes", "bet": "Tous les paris sont affichés ici", "bonus": "Tous les bonus sont affichés ici"}, "betAmount": "<PERSON><PERSON> du pari", "win": "<PERSON><PERSON><PERSON><PERSON>"}, "withdraw": {"balance": "Solde retirable", "amount": "Montant du retrait", "limit": "Plage autorisée par opération : {min} ~ {max}", "submit": "<PERSON><PERSON><PERSON> maintenant", "addWithdrawalAccount": "Ajouter un nouveau compte de retrait", "method": {"bank": "Carte bancaire", "usdt": "USDT"}, "form": {"bank": {"account": "Numéro de compte bancaire", "ifsc": "Code IFSC", "branch": "Agence/Nom", "bankname": "Nom de la banque (facultatif)", "email": "Email (facultatif)", "state": "État/Province (facultatif)"}, "usdt": {"addr": "Adresse USDT", "network": "Type de réseau"}}, "hint": {"account": "Veuillez renseigner un compte à votre nom", "ifsc": "L'IFSC sera en majuscules et sans espaces", "erc20": "Assurez-vous que l'adresse est sur le réseau ERC20", "btc": "Assurez-vous que l'adresse est sur le réseau BTC"}}}