import { createI18n } from 'vue-i18n'
import zh_CN from './messages/zh_CN.json'
import en_US from './messages/en_US.json'
import fr_FR from './messages/fr_FR.json'
import pt_PT from './messages/pt_PT.json'
import es_ES from './messages/es_ES.json'

const DEFAULT_LOCALE = 'en_US' as const
export const SUPPORTED_LOCALES = ['zh_CN', 'en_US', 'fr_FR', 'pt_PT', 'es_ES'] as const
export type LocaleKey = (typeof SUPPORTED_LOCALES)[number]

function normalizeLocale(input: string | null | undefined): LocaleKey {
  const code = (input || '').toLowerCase()
  if (code.includes('zh')) return 'zh_CN'
  if (code.includes('fr')) return 'fr_FR'
  if (code.includes('pt')) return 'pt_PT'
  if (code.includes('es')) return 'es_ES'
  if (code.includes('en')) return 'en_US'
  return DEFAULT_LOCALE
}

function resolveInitialLocale(): LocaleKey {
  const saved = localStorage.getItem('locale')
  if (saved) return normalizeLocale(saved)
  return normalizeLocale(navigator.language)
}

export const i18n = createI18n({
  legacy: false,
  locale: resolveInitialLocale(),
  messages: { zh_CN, en_US, fr_FR, pt_PT, es_ES },
  fallbackLocale: 'en_US',
  globalInjection: true,
})

export type AppMessageSchema = typeof zh_CN
