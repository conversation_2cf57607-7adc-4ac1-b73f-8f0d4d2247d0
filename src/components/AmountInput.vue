<template>
  <div class="amount-input">
    <div class="ai-row">
      <h3 v-if="title" class="amount-title">{{ title }}</h3>
      <div class="ai-input" :style="{ width: normalizeWidth(props.width) }">
        <el-input-number
          :model-value="modelValue"
          :min="min"
          :max="max"
          :step="step"
          controls-position="right"
          @update:model-value="onUpdate"
        >
          <template #decrease-icon>
            <el-icon><Minus /></el-icon>
          </template>
          <template #increase-icon>
            <el-icon><Plus /></el-icon>
          </template>
          <template #prefix>
            <span class="amount-prefix">$</span>
          </template>
          <template #suffix>
            <el-button link type="danger" v-if="modelValue" @click="$emit('update:modelValue', 0)">
              <el-icon><Close /></el-icon>
            </el-button>
          </template>
        </el-input-number>
      </div>
    </div>
    <div v-if="hint" class="hint">{{ hint }}</div>
  </div>
</template>

<script setup lang="ts">
import { Minus, Plus, Close } from '@element-plus/icons-vue'

const props = defineProps<{
  modelValue: number
  min?: number
  max?: number
  step?: number
  title?: string
  hint?: string
  width?: number | string
}>()

const emit = defineEmits<{ 'update:modelValue': [number] }>()

function onUpdate(v: number | undefined) {
  emit('update:modelValue', Number(v ?? 0))
}

function normalizeWidth(w?: number | string) {
  if (w === undefined) return '300px'
  if (typeof w === 'number') return `${w}px`
  return w
}
</script>

<style scoped>
.amount-input {
  display: flex;
  flex-direction: column;
}
.ai-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
  width: 100%;
  flex-wrap: nowrap;
}
.amount-title {
  margin: 0;
  border-left: 3px solid #20a37a;
  padding-left: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.ai-input {
  flex: 0 0 auto;
  margin-left: auto;
}
.ai-input :deep(.el-input-number) {
  width: 100%;
}
.amount-prefix {
  font-weight: 700;
}
.hint {
  margin-top: 6px;
  color: var(--el-text-color-secondary);
  font-size: 12px;
}

/* 移动端也保持左右布局，不再换行 */
</style>
