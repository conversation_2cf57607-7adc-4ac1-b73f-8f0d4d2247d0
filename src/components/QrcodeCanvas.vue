<template>
  <canvas ref="canvasRef" class="qrcode" aria-label="qrcode" />
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onBeforeUnmount } from 'vue'
import QRCode from 'qrcode'

const props = defineProps<{ text: string; size?: number }>()
defineOptions({ name: 'QrcodeCanvas' })
const canvasRef = ref<HTMLCanvasElement | null>(null)

function draw(text: string) {
  if (!canvasRef.value || !text) return
  QRCode.toCanvas(canvasRef.value, text, { width: props.size || 200 })
}

onMounted(() => draw(props.text))
watch(
  () => props.text,
  (n) => draw(n),
)
onBeforeUnmount(() => {})
</script>

<style scoped>
.qrcode {
  width: 220px;
  height: 220px;
}
</style>
