<template>
  <header
    class="app-header-bar"
    :class="{ 'is-app': variant === 'sticky', 'is-inline': variant !== 'sticky' }"
  >
    <el-button v-if="showBack" class="nav-btn" text @click="goBack" aria-label="Back">
      <el-icon :size="18" color="#fff"><ArrowLeft /></el-icon>
    </el-button>
    <div class="title">{{ t(titleKey) }}</div>
    <div class="actions">
      <!-- 语言选择（靠左） -->
      <el-select v-model="locale" class="lang-select" size="small" :teleported="false">
        <el-option
          v-for="opt in localeOptions"
          :key="opt.value"
          :label="opt.label"
          :value="opt.value"
        />
      </el-select>
      <el-tooltip v-if="supportUrl" :content="t('common.customerService')" placement="bottom">
        <el-button class="nav-btn" text @click="openSupport" aria-label="Support">
          <el-icon :size="18" color="#fff"><Headset /></el-icon>
        </el-button>
      </el-tooltip>
      <slot name="right" />
    </div>
  </header>
  <div v-if="variant === 'sticky'" class="header-spacer" />
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { ArrowLeft, Headset } from '@element-plus/icons-vue'

const props = defineProps<{
  showBack?: boolean
  titleKey: string
  supportUrl?: string | null
  variant?: 'sticky' | 'inline'
}>()

const { t, locale } = useI18n()
const router = useRouter()

const showBack = computed(() => props.showBack !== false)
const variant = computed(() => props.variant ?? 'inline')

const localeOptions = [
  { value: 'zh_CN', label: '中文' },
  { value: 'en_US', label: 'English' },
  { value: 'fr_FR', label: 'Français' },
  { value: 'pt_PT', label: 'Português' },
  { value: 'es_ES', label: 'Español' },
]

watch(
  () => locale.value,
  (val) => {
    try {
      localStorage.setItem('locale', String(val))
    } catch {}
  },
)

function goBack() {
  if (window.history.length > 1) router.back()
  else router.push('/')
}

function openSupport() {
  if (!props.supportUrl) return
  window.open(props.supportUrl, '_blank', 'noopener')
}
</script>

<style scoped lang="scss">
.app-header-bar {
  display: grid;
  grid-template-columns: 48px 1fr auto;
  align-items: center;
}
.is-app {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 56px;
  color: #fff;
  background: #0c7f6c;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(14, 138, 113, 0.15);
}

.is-inline {
  height: 42px;
  margin: 6px 0 10px;
  color: #fff;
}
.header-spacer {
  height: 56px;
}
.title {
  text-align: center;
  font-weight: 700;
  letter-spacing: 0.3px;
}
.actions {
  display: flex;
  justify-content: flex-end;
  gap: 6px;
  color: #fff;
}

.nav-btn {
  color: #fff;
  // 保持深色背景下的可见性
  &:hover,
  &:focus,
  &:active {
    background-color: rgba(255, 255, 255, 0.12) !important;
    color: #fff !important;
  }
}

// 语言选择在深色背景下的适配
.lang-select {
  width: 98px;
  :deep(.el-select__wrapper),
  :deep(.el-input__wrapper) {
    background-color: transparent !important;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.35) inset;
    color: #fff;
  }
  :deep(.el-input__inner),
  :deep(.el-select__selected-item) {
    color: #fff !important;
  }
  :deep(.el-select__caret) {
    color: #fff !important;
  }
}
</style>
