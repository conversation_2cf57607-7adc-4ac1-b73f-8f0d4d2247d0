@use 'sass:color';

// Element Plus 样式与主题变量（按需 SASS）
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  $colors: (
    'primary': (
      'base': #0e8a71,
    ),
  )
);
@use 'element-plus/theme-chalk/src/index.scss' as *;

:root {
  // 基础字体大小 - 将由 useViewport 动态设置
  --app-font-size: 16px;
  --el-font-size-base: var(--app-font-size);
  --el-font-size-small: calc(var(--app-font-size) - 2px);
  --el-font-size-large: calc(var(--app-font-size) + 2px);

  // 组件尺寸 - 移动端友好
  --el-component-size: 44px;
  --el-component-size-large: 48px;
  --el-input-height: 44px;
  --el-input-height-large: 48px;

  // 间距系统
  --app-spacing: 16px;
  --app-spacing-large: 24px;
  --app-spacing-small: 8px;

  // 视觉样式
  --app-bg: #f7f8fa;
  --card-radius: 12px;
  --shadow-soft: 0 6px 20px rgba(0, 0, 0, 0.05);
  --glass: saturate(180%) blur(8px);
}

html,
body,
#app {
  height: 100%;
}

html {
  font-size: var(--app-font-size);
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

body {
  margin: 0;
  background: var(--app-bg);
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans',
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji', sans-serif;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

button,
input,
select,
textarea {
  font: inherit;
}

// 全局卡片细化
:root .el-card {
  border-radius: var(--card-radius);
  box-shadow: var(--shadow-soft);
}

// 移动端优化
@media (max-width: 768px) {
  :root {
    // 移动端更大的触摸目标
    --el-button-size: 44px;
    --el-input-height: 44px;
  }

  .el-card {
    border-radius: 10px;
    margin: var(--app-spacing-small);
  }

  // 按钮最小触摸区域
  .el-button {
    min-height: 44px;
    padding: 12px 16px;
    font-size: var(--el-font-size-base);
  }

  // 输入框优化
  .el-input__inner,
  .el-textarea__inner {
    font-size: var(--el-font-size-base);
    padding: 12px 16px;
  }

  // 表单项间距
  .el-form-item {
    margin-bottom: var(--app-spacing-large);
  }

  // 标签文字大小
  .el-form-item__label {
    font-size: var(--el-font-size-base);
    font-weight: 500;
  }
}

a {
  color: var(--el-color-primary);
  text-decoration: none;
}

// 移动端全局优化
@media (max-width: 768px) {
  // 确保文本可读性
  body {
    font-size: var(--app-font-size);
    line-height: 1.6;
  }

  // 标题层级优化
  h1 {
    font-size: calc(var(--app-font-size) * 1.8);
  }
  h2 {
    font-size: calc(var(--app-font-size) * 1.6);
  }
  h3 {
    font-size: calc(var(--app-font-size) * 1.4);
  }
  h4 {
    font-size: calc(var(--app-font-size) * 1.2);
  }
  h5,
  h6 {
    font-size: var(--app-font-size);
  }

  // 链接和按钮的触摸优化
  a,
  button,
  .el-button {
    min-height: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  // 表格在移动端的优化
  .el-table {
    font-size: var(--app-font-size);

    .el-table__cell {
      padding: 12px 8px;
    }
  }

  // 对话框和抽屉优化
  .el-dialog,
  .el-drawer {
    .el-dialog__header,
    .el-drawer__header {
      font-size: calc(var(--app-font-size) * 1.2);
      font-weight: 600;
    }

    .el-dialog__body,
    .el-drawer__body {
      font-size: var(--app-font-size);
      line-height: 1.6;
    }
  }

  // 消息提示优化
  .el-message,
  .el-notification {
    font-size: var(--app-font-size);
  }
}
