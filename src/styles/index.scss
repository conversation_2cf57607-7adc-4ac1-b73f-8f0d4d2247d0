@use 'sass:color';

// Element Plus 样式与主题变量（按需 SASS）
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  $colors: (
    'primary': (
      'base': #0e8a71,
    ),
  )
);
@use 'element-plus/theme-chalk/src/index.scss' as *;

:root {
  --app-font-size: clamp(14px, 3.2vw, 16px);
  --el-font-size-base: var(--app-font-size);
  --el-font-size-small: calc(var(--app-font-size) - 2px);
  --el-font-size-large: calc(var(--app-font-size) + 2px);
  --app-bg: #f7f8fa;
  --card-radius: 12px;
  --shadow-soft: 0 6px 20px rgba(0, 0, 0, 0.05);
  --glass: saturate(180%) blur(8px);
}

html,
body,
#app {
  height: 100%;
}

html {
  font-size: var(--app-font-size);
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

body {
  margin: 0;
  background: var(--app-bg);
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans',
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji', sans-serif;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

button,
input,
select,
textarea {
  font: inherit;
}

// 全局卡片细化
:root .el-card {
  border-radius: var(--card-radius);
  box-shadow: var(--shadow-soft);
}

// 自适应改进：减少移动端边距
@media (max-width: 480px) {
  :root .el-card {
    border-radius: 10px;
  }
}

a {
  color: var(--el-color-primary);
  text-decoration: none;
}
