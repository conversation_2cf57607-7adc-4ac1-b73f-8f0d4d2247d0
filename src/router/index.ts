import {
  createRouter,
  createWebHistory,
  type RouteRecordRaw,
  type RouteLocationNormalized,
} from 'vue-router'

export const routes: RouteRecordRaw[] = [
  {
    path: '/deposit',
    component: () => import('@/views/deposit/Index.vue'),
    meta: { title: 'page.deposit' },
  },
  {
    path: '/history',
    component: () => import('@/views/history/Index.vue'),
    meta: { title: 'page.history' },
  },
  {
    path: '/withdraw',
    component: () => import('@/views/withdraw/Index.vue'),
    meta: { title: 'page.withdraw' },
  },
  {
    path: '/withdraw/addbank', // 提现添加银行卡
    component: () => import('@/views/withdraw/AddBank.vue'),
    meta: { title: 'withdraw.addWithdrawalAccount' },
  },
  {
    path: '/cashier',
    component: () => import('@/views/cashier/Index.vue'),
    meta: { title: 'page.cashier' },
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
})

function extractUidToken(to: RouteLocationNormalized): { uid?: string; token?: string } {
  const getFirstString = (val: unknown): string | undefined => {
    if (typeof val === 'string') return val
    if (Array.isArray(val) && typeof val[0] === 'string') return val[0]
    return undefined
  }

  const q = to.query as Record<string, unknown>
  const uidFromQuery = getFirstString(q?.uid)
  const tokenFromQuery = getFirstString(q?.token)
  if (uidFromQuery && tokenFromQuery) return { uid: uidFromQuery, token: tokenFromQuery }

  const afterHash = to.fullPath.split('#')[1] || ''
  const queryString = afterHash.split('?')[1] || ''
  if (!queryString) return {}
  const sp = new URLSearchParams(queryString)
  const uid = sp.get('uid') || undefined
  const token = sp.get('token') || undefined
  return { uid, token }
}

// 设置页面标题
router.afterEach((to, from) => {
  const { uid, token } = extractUidToken(to)
  if (uid && token) {
    localStorage.setItem('uid', uid)
    localStorage.setItem('token', token)
  }
  import('@/locales').then(({ i18n }) => {
    const t = i18n.global.t
    const titleKey = (to.meta?.title as string) || 'app.name'
    document.title = `${t(titleKey)} - ${t('app.name')}`
  })
})
export default router
