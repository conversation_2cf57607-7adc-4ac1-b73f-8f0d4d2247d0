/**
 * 移动端优化工具函数
 */

// 检测是否为移动设备
export function isMobileDevice(): boolean {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

// 检测是否为触摸设备
export function isTouchDevice(): boolean {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0
}

// 获取视口尺寸
export function getViewportSize() {
  return {
    width: Math.max(document.documentElement.clientWidth || 0, window.innerWidth || 0),
    height: Math.max(document.documentElement.clientHeight || 0, window.innerHeight || 0)
  }
}

// 设置视口meta标签（防止缩放）
export function setViewportMeta() {
  let viewport = document.querySelector('meta[name=viewport]')
  if (!viewport) {
    viewport = document.createElement('meta')
    viewport.setAttribute('name', 'viewport')
    document.head.appendChild(viewport)
  }
  viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no')
}

// 禁用双击缩放
export function disableDoubleTapZoom() {
  let lastTouchEnd = 0
  document.addEventListener('touchend', (event) => {
    const now = new Date().getTime()
    if (now - lastTouchEnd <= 300) {
      event.preventDefault()
    }
    lastTouchEnd = now
  }, false)
}

// 移动端安全区域适配
export function getSafeAreaInsets() {
  const style = getComputedStyle(document.documentElement)
  return {
    top: parseInt(style.getPropertyValue('--sat') || '0'),
    right: parseInt(style.getPropertyValue('--sar') || '0'),
    bottom: parseInt(style.getPropertyValue('--sab') || '0'),
    left: parseInt(style.getPropertyValue('--sal') || '0')
  }
}

// 设置安全区域CSS变量
export function setSafeAreaVars() {
  const root = document.documentElement
  root.style.setProperty('--sat', 'env(safe-area-inset-top)')
  root.style.setProperty('--sar', 'env(safe-area-inset-right)')
  root.style.setProperty('--sab', 'env(safe-area-inset-bottom)')
  root.style.setProperty('--sal', 'env(safe-area-inset-left)')
}

// 移动端字体大小调整
export function adjustFontSize(scale: number = 1.2) {
  if (isMobileDevice()) {
    const root = document.documentElement
    const currentSize = parseInt(getComputedStyle(root).getPropertyValue('--app-font-size'))
    root.style.setProperty('--app-font-size', `${Math.round(currentSize * scale)}px`)
  }
}

// 初始化移动端优化
export function initMobileOptimization() {
  if (isMobileDevice() || isTouchDevice()) {
    setViewportMeta()
    setSafeAreaVars()
    disableDoubleTapZoom()
    
    // 添加移动端类名
    document.body.classList.add('is-mobile')
    
    // 监听方向变化
    window.addEventListener('orientationchange', () => {
      setTimeout(() => {
        // 方向变化后重新计算
        const { width, height } = getViewportSize()
        document.documentElement.style.setProperty('--viewport-width', `${width}px`)
        document.documentElement.style.setProperty('--viewport-height', `${height}px`)
      }, 100)
    })
  }
}
