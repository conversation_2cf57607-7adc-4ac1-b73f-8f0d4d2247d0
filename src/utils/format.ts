export function formatCurrency(amount: number): string {
  return `$${amount.toFixed(2)}`
}

export function htmlBrToLines(raw: string | undefined | null): string[] {
  if (!raw) return []
  return raw
    .split(/<br\s*\/?>(?:\n)?/i)
    .map((s) => s.trim())
    .filter(Boolean)
}

export function inRange(n: number, min: number, max: number): boolean {
  if (Number.isNaN(n)) return false
  return n >= min && n <= max
}

export function parseStoredAmount(value: string | null | undefined, fallback = 200): number {
  if (!value) return fallback
  try {
    const parsed = JSON.parse(value)
    if (typeof parsed === 'number') return parsed
    if (parsed && typeof parsed === 'object' && typeof parsed.data === 'number') return parsed.data
  } catch {
    // not JSON
  }
  const num = Number(value)
  return Number.isFinite(num) ? num : fallback
}

// 带符号的货币格式化：
// - 当 showSign 为 true 时：根据原始值是否带负号决定前缀 '-' 或 '+'，始终显示符号。
// - 当 showSign 为 false 时：不显示符号，仅显示 $ 与绝对值。
export function formatSignedCurrency(value: string | number | undefined, showSign = true): string {
  if (value === undefined || value === null) return '$0.00'
  const raw = String(value)
  const abs = raw.replace(/^[-+]/, '')
  if (!showSign) return `$${abs}`
  const negative = /^-/.test(raw)
  const prefix = negative ? '-' : '+'
  return `${prefix}$${abs}`
}
