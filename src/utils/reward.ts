import type { DepositData, DepositItem } from '@/types/api/deposit'

/**
 * 解析百分比
 */
function parsePercent(input?: number | string | null): number | null {
  if (input == null) return null
  if (typeof input === 'number') {
    if (input > 0 && input < 1) return input
    if (input >= 1 && input <= 100) return input / 100
    return null
  }
  const hasPercent = /%/.test(input)
  const num = parseFloat(input)
  if (!Number.isFinite(num)) return null
  return hasPercent || num > 1 ? num / 100 : num
}

/**
 * 解析固定金额
 */
function parseFixed(input?: number | string | null): number | null {
  if (typeof input === 'number') return input > 0 ? input : null
  if (typeof input === 'string' && !/%/.test(input)) {
    const num = parseFloat(input.replace(/[^0-9.]/g, ''))
    return Number.isFinite(num) && num > 0 ? num : null
  }
  return null
}

/**
 * 计算充值奖励金额（优先比例，然后固定）
 */
export function computeCashBonus(amount: number, input?: number | string | null): number {
  const percent = parsePercent(input)
  if (percent != null) return +(amount * percent).toFixed(2)
  const fixed = parseFixed(input)
  return fixed != null ? +(+fixed).toFixed(2) : 0
}
