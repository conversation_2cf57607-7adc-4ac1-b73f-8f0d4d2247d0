// 通用状态映射工具，避免在各处重复实现
export type HistoryTab = 'deposit' | 'withdraw' | 'bet' | 'bonus'
export type StatusType = 'success' | 'warning' | 'danger' | 'info'
export type LegendKey =
  | 'success'
  | 'inProcess'
  | 'failed'
  | 'refund'
  | 'win'
  | 'loss'
  | 'transferIn'
  | 'transferOut'

export const isNegativeAmount = (coin: string | number | undefined): boolean => {
  if (coin === undefined || coin === null) return false
  return String(coin).trim().startsWith('-')
}

// 图标/类名
export function getStatusClass(tab: HistoryTab, item: any): string {
  const s = Number(item?.status)
  switch (tab) {
    case 'deposit':
      if (s === 0 || s === 1) return 'status-pending'
      if (s === 2 || s === 3) return 'status-success'
      if (s === 4) return 'status-fail'
      return 'status-pending'
    case 'withdraw':
      if (s === 0 || s === 1) return 'status-pending'
      if (s === 2) return 'status-success'
      if (s === 3) return 'status-fail'
      return 'status-pending'
    case 'bet':
      if (s === 2) return 'status-win'
      if (s === 3) return 'status-loss'
      return 'status-pending'
    case 'bonus':
      return isNegativeAmount(item?.coin) ? 'status-out' : 'status-in'
    default:
      return 'status-pending'
  }
}

// 标签类型
export function getStatusType(tab: HistoryTab, item: any): StatusType {
  const s = Number(item?.status)
  switch (tab) {
    case 'deposit':
      if (s === 0 || s === 1) return 'warning'
      if (s === 2 || s === 3) return 'success'
      if (s === 4) return 'danger'
      return 'info'
    case 'withdraw':
      if (s === 0 || s === 1) return 'warning'
      if (s === 2) return 'success'
      if (s === 3) return 'danger'
      return 'info'
    case 'bet':
      return s === 2 ? 'success' : 'danger'
    case 'bonus':
      return isNegativeAmount(item?.coin) ? 'danger' : 'success'
    default:
      return 'info'
  }
}

// 过滤标签键
export function getLegendKey(tab: HistoryTab, item: any): LegendKey {
  const s = Number(item?.status)
  switch (tab) {
    case 'deposit':
      if (s === 2 || s === 3) return 'success'
      if (s === 4) return 'failed'
      if (s === 0 || s === 1) return 'inProcess'
      return 'failed'
    case 'withdraw':
      if (s === 2) return 'success'
      if (s === 3) return 'failed'
      if (s === 0 || s === 1) return 'inProcess'
      return 'failed'
    case 'bet':
      return s === 2 ? 'win' : 'loss'
    case 'bonus':
      return isNegativeAmount(item?.coin) ? 'transferOut' : 'transferIn'
    default:
      return 'failed'
  }
}

// 状态文案
export function getStatusText(tab: HistoryTab, item: any, t: (key: string) => string): string {
  const s = Number(item?.status)
  switch (tab) {
    case 'bet':
      return s === 2 ? t('history.legend.win') : t('history.legend.loss')
    case 'bonus':
      return isNegativeAmount(item?.coin)
        ? t('history.legend.transferOut')
        : t('history.legend.transferIn')
    case 'deposit':
      if (s === 0 || s === 1) return t('history.legend.inProcess')
      if (s === 2 || s === 3) return t('history.legend.success')
      if (s === 4) return t('history.legend.failed')
      return t('history.legend.inProcess')
    case 'withdraw':
      if (s === 0 || s === 1) return t('history.legend.inProcess')
      if (s === 2) return t('history.legend.success')
      if (s === 3) return t('history.legend.failed')
      return t('history.legend.inProcess')
    default:
      return ''
  }
}
