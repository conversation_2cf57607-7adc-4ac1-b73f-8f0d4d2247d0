<template>
  <div class="withdraw-page">
    <main class="content">
      <!-- 可提现余额 -->
      <section class="balance-card">
        <div class="txt">{{ t('withdraw.balance') }}</div>
        <div class="coin">{{ formatCurrency(store.data?.dcoin ?? 0) }}</div>
      </section>

      <!-- 金额输入 -->
      <section class="amount">
        <AmountInput
          v-model="store.amount"
          :min="0"
          :step="10"
          :title="t('withdraw.amount')"
          :width="160"
        />
        <div class="quick-row">
          <el-button v-for="v in quickAmounts" :key="v" size="small" @click="store.amount = v"
            >${{ v }}</el-button
          >
          <el-button size="small" text @click="store.amount = store.data?.mincoin ?? 0"
            >Min</el-button
          >
          <el-button size="small" text @click="store.amount = store.data?.maxcoin ?? 0"
            >Max</el-button
          >
        </div>
        <div class="tips">
          {{
            t('withdraw.limit', {
              min: formatCurrency(store.data?.mincoin ?? 0),
              max: formatCurrency(store.data?.maxcoin ?? 0),
            })
          }}
        </div>
      </section>

      <!-- 银行卡列表 -->
      <section class="banks">
        <el-card class="bank add" @click="$router.push('/withdraw/addbank')">
          <div class="item">
            <el-icon><Plus /></el-icon>
            <span>{{ t('withdraw.addWithdrawalAccount') }}</span>
          </div>
        </el-card>
        <template v-if="store.banks?.length">
          <el-card
            v-for="b in store.banks"
            :key="b.id"
            class="bank"
            :class="{ active: store.selectedBankId === b.id }"
            @click="store.selectBank(b.id)"
          >
            <div class="item">
              <div class="meta">
                <div class="name">{{ b.name }}</div>
                <div class="card">{{ b.card }}</div>
              </div>
              <el-icon><ArrowRight /></el-icon>
            </div>
          </el-card>
        </template>
        <el-empty v-else :description="t('withdraw.addWithdrawalAccount')" />
      </section>

      <!-- 提交按钮 -->
      <section class="submit">
        <el-button
          type="primary"
          :disabled="!store.canSubmit"
          :loading="btnLoading"
          @click="handleSubmit"
        >
          {{ t('withdraw.submit') }}
        </el-button>
      </section>

      <!-- 说明 -->
      <section class="desc" v-if="store.data?.memo" v-html="store.data?.memo" />

      <el-alert
        v-if="store.error"
        type="error"
        :title="String(store.error)"
        show-icon
        style="margin-top: 10px"
      />
    </main>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import AmountInput from '@/components/AmountInput.vue'
import { useI18n } from 'vue-i18n'
import { Plus, ArrowRight } from '@element-plus/icons-vue'
import { useAppBarStore } from '@/stores/appbar'
import { useWithdrawStore } from '@/stores/withdraw'
import { ElMessage } from 'element-plus'
import { formatCurrency } from '@/utils/format'

const { t } = useI18n()
const store = useWithdrawStore()
const appBarStore = useAppBarStore()
const btnLoading = ref(false)
const quickAmounts = [100, 500, 1000, 2000]

onMounted(async () => {
  await store.fetchInit()
  appBarStore.set({
    showBack: true,
    titleKey: 'page.withdraw',
    supportUrl: store.data?.url || null,
  })
})

// 统一使用 utils/format

async function handleSubmit() {
  try {
    btnLoading.value = true
    await store.submit()
    ElMessage.success(t('common.tips'))
  } catch (e) {
    ElMessage.error((e as Error)?.message || 'Submit failed')
  } finally {
    btnLoading.value = false
  }
}
</script>

<style scoped lang="scss">
.withdraw-page {
  margin: 0 auto;
  padding: 12px;
}

.balance-card {
  margin: 8px 0 12px;
  background: linear-gradient(135deg, #f0fdf4, #ecfeff);
  border: 1px solid #d9f7be;
  border-radius: 10px;
  padding: 12px;
  .txt {
    color: var(--el-text-color-secondary);
    font-size: 12px;
  }
  .coin {
    font-size: 26px;
    font-weight: 800;
    letter-spacing: -0.5px;
  }
}
.banks {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
  .bank {
    cursor: pointer;
    &.active {
      outline: 2px solid var(--el-color-primary);
    }
    .item {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .meta {
      display: flex;
      gap: 8px;
      align-items: center;
      .name {
        font-weight: 600;
      }
      .card {
        color: var(--el-text-color-secondary);
        font-family: monospace;
      }
    }
  }

  .add .item {
    gap: 6px;
  }
}
.amount {
  margin-top: 12px;
  .label {
    font-weight: 600;
    margin-bottom: 6px;
  }
  .input {
    display: flex;
    align-items: center;
    gap: 6px;
  }
  .currency {
    font-weight: 700;
  }
  .quick-row {
    margin-top: 8px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }
  .tips {
    margin-top: 6px;
    color: var(--el-text-color-secondary);
    font-size: 12px;
  }
}
.submit {
  margin-top: 12px;
}
.desc {
  margin-top: 12px;
  color: var(--el-text-color-secondary);
  font-size: 12px;
}
</style>
