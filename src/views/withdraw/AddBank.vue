<template>
  <div class="add-bank-page">
    <el-card v-loading="submitting">
      <template #header>
        <div class="card-header">
          <el-radio-group v-model="mode">
            <!-- <el-radio-button label="bank">{{ t('withdraw.method.bank') }}</el-radio-button> -->
            <el-radio-button label="usdt">{{ t('withdraw.method.usdt') }}</el-radio-button>
          </el-radio-group>
        </div>
      </template>
      <!-- 银行卡表单 -->
      <el-form
        v-if="mode === 'bank'"
        ref="formRef"
        :model="form"
        :rules="rules"
        label-position="top"
      >
        <el-form-item :label="t('withdraw.form.bank.account')" prop="account">
          <el-input
            ref="accountInputRef"
            v-model.trim="form.account"
            :placeholder="t('withdraw.form.bank.account')"
            maxlength="32"
            show-word-limit
            @keyup.enter="onSubmit"
            autocomplete="off"
          >
            <template #append>
              <el-button link type="primary" @click="pasteTo('account')">{{
                t('common.paste')
              }}</el-button>
            </template>
          </el-input>
          <div class="hint">{{ t('withdraw.hint.account') }}</div>
        </el-form-item>
        <el-form-item :label="t('withdraw.form.bank.ifsc')" prop="ifsc">
          <el-input
            v-model.trim="form.ifsc"
            :placeholder="t('withdraw.form.bank.ifsc')"
            maxlength="16"
            @input="onIfscInput"
            @keyup.enter="onSubmit"
            autocomplete="off"
          >
            <template #append>
              <el-button link type="primary" @click="pasteTo('ifsc')">{{
                t('common.paste')
              }}</el-button>
            </template>
          </el-input>
          <div class="hint">{{ t('withdraw.hint.ifsc') }}</div>
        </el-form-item>
        <el-form-item :label="t('withdraw.form.bank.branch')" prop="username">
          <el-input
            v-model.trim="form.username"
            :placeholder="t('withdraw.form.bank.branch')"
            maxlength="50"
            @keyup.enter="onSubmit"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item :label="t('withdraw.form.bank.bankname')" prop="bankname">
          <el-input
            v-model.trim="form.bankname"
            :placeholder="t('withdraw.form.bank.bankname')"
            maxlength="50"
            @keyup.enter="onSubmit"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item :label="t('withdraw.form.bank.email')" prop="email">
          <el-input
            v-model.trim="form.email"
            :placeholder="t('withdraw.form.bank.email')"
            maxlength="60"
            @keyup.enter="onSubmit"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item :label="t('withdraw.form.bank.state')" prop="state">
          <el-input
            v-model.trim="form.state"
            :placeholder="t('withdraw.form.bank.state')"
            maxlength="40"
            @keyup.enter="onSubmit"
            autocomplete="off"
          />
        </el-form-item>
        <div class="section-more">
          <el-button text type="primary" @click="showMore = !showMore">
            {{ showMore ? t('common.less') : t('common.more') }}
          </el-button>
        </div>
        <el-collapse-transition>
          <div v-show="showMore">
            <el-form-item :label="t('withdraw.form.bank.bankname')" prop="bankname">
              <el-input
                v-model.trim="form.bankname"
                :placeholder="t('withdraw.form.bank.bankname')"
                maxlength="50"
                @keyup.enter="onSubmit"
                autocomplete="off"
              />
            </el-form-item>
            <el-form-item :label="t('withdraw.form.bank.email')" prop="email">
              <el-input
                v-model.trim="form.email"
                :placeholder="t('withdraw.form.bank.email')"
                maxlength="60"
                @keyup.enter="onSubmit"
                autocomplete="off"
              />
            </el-form-item>
          </div>
        </el-collapse-transition>
        <el-form-item>
          <el-button
            type="primary"
            :loading="submitting"
            :disabled="disableBankSubmit"
            @click="onSubmit"
            >{{ t('common.submit') }}</el-button
          >
          <el-button @click="$router.back()">{{ t('common.cancel') }}</el-button>
        </el-form-item>
      </el-form>

      <!-- USDT 表单 -->
      <el-form v-else ref="formRefUsdt" :model="formUsdt" :rules="rulesUsdt" label-position="top">
        <el-form-item :label="t('withdraw.form.usdt.addr')" prop="addr">
          <el-input
            ref="addrInputRef"
            v-model.trim="formUsdt.addr"
            :placeholder="t('withdraw.form.usdt.addr')"
            maxlength="128"
            @keyup.enter="onSubmitUsdt"
            autocomplete="off"
          >
            <template #append>
              <el-button link type="primary" @click="pasteTo('addr')">{{
                t('common.paste')
              }}</el-button>
            </template>
          </el-input>
          <div class="hint" v-if="formUsdt.type === 1">{{ t('withdraw.hint.erc20') }}</div>
          <div class="hint" v-else>{{ t('withdraw.hint.btc') }}</div>
        </el-form-item>
        <el-form-item :label="t('withdraw.form.usdt.network')" prop="type">
          <el-select v-model="formUsdt.type" style="width: 100%">
            <el-option :label="'TRC20'" :value="1" />
            <!-- <el-option :label="'ERC20'" :value="2" /> -->
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :loading="submitting"
            :disabled="disableUsdtSubmit"
            @click="onSubmitUsdt"
            >{{ t('common.submit') }}</el-button
          >
          <el-button @click="$router.back()">{{ t('common.cancel') }}</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { addBankAccount, addUsdtAccount } from '@/api'
import { useAppBarStore } from '@/stores/appbar'

const { t } = useI18n()
const appBarStore = useAppBarStore()

const formRef = ref<FormInstance>()
const formRefUsdt = ref<FormInstance>()
const accountInputRef = ref()
const addrInputRef = ref()
const mode = ref<'bank' | 'usdt'>('usdt')
const submitting = ref(false)
const showMore = ref(false)
const form = ref({
  account: '',
  username: '',
  bankname: '',
  ifsc: '',
  email: '',
  state: '',
})
const formUsdt = ref<{ addr: string; type: number }>({ addr: '', type: 1 })

const rules = ref<FormRules>({
  account: [
    { required: true, message: t('withdraw.form.bank.account'), trigger: 'blur' },
    { min: 6, message: t('common.tips'), trigger: 'blur' },
  ],
  ifsc: [{ required: true, message: t('withdraw.form.bank.ifsc'), trigger: 'blur' }],
  username: [{ required: true, message: t('withdraw.form.bank.branch'), trigger: 'blur' }],
})
const rulesUsdt = ref<FormRules>({
  addr: [{ required: true, message: t('withdraw.form.usdt.addr'), trigger: 'blur' }],
  type: [{ required: true, message: t('withdraw.form.usdt.network'), trigger: 'change' }],
})

onMounted(() => {
  appBarStore.set({ showBack: true, titleKey: 'withdraw.addWithdrawalAccount' })
  nextTick(() => {
    if (mode.value === 'bank' && accountInputRef.value) accountInputRef.value.focus()
    if (mode.value === 'usdt' && addrInputRef.value) addrInputRef.value.focus()
  })
})

async function onSubmit() {
  if (!formRef.value) return
  try {
    await formRef.value.validate()
  } catch {
    return
  }
  try {
    submitting.value = true
    await addBankAccount(form.value)
    ElMessage.success(t('common.added'))
    history.length > 1 ? history.back() : (location.href = '/withdraw')
  } catch (e) {
    ElMessage.error((e as Error)?.message || t('common.tips'))
  } finally {
    submitting.value = false
  }
}

async function onSubmitUsdt() {
  if (!formRefUsdt.value) return
  try {
    await formRefUsdt.value.validate()
  } catch {
    return
  }
  try {
    submitting.value = true
    await addUsdtAccount(formUsdt.value)
    ElMessage.success(t('common.added'))
    history.length > 1 ? history.back() : (location.href = '/withdraw')
  } catch (e) {
    ElMessage.error((e as Error)?.message || t('common.tips'))
  } finally {
    submitting.value = false
  }
}

// UX: IFSC 自动大写并去空格
function onIfscInput() {
  form.value.ifsc = form.value.ifsc.replace(/\s+/g, '').toUpperCase()
}

// UX: 禁用提交按钮的条件，提升表单交互体验
const disableBankSubmit = computed(
  () => submitting.value || !form.value.account || !form.value.ifsc || !form.value.username,
)
const disableUsdtSubmit = computed(
  () => submitting.value || !formUsdt.value.addr || !formUsdt.value.type,
)

// 便捷粘贴
async function pasteTo(field: 'account' | 'ifsc' | 'addr') {
  try {
    const text = await navigator.clipboard.readText()
    if (!text) return
    if (field === 'account') form.value.account = text.trim()
    if (field === 'ifsc') form.value.ifsc = text.trim().toUpperCase()
    if (field === 'addr') formUsdt.value.addr = text.trim()
  } catch {}
}
</script>

<style scoped>
.add-bank-page {
  padding: 12px;
}
.card-header {
  font-weight: 700;
}
.hint {
  margin-top: 4px;
  color: var(--el-text-color-secondary);
  font-size: 12px;
}
.section-more {
  display: flex;
  justify-content: flex-end;
}
</style>
