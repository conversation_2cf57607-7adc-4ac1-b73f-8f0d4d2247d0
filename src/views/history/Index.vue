<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="history-page">
    <!-- 固定头部 -->
    <header class="history-header">
      <div class="header-container">
        <!-- Tab切换 -->
        <el-tabs v-model="store.active" class="history-tabs" stretch>
          <el-tab-pane
            v-for="tab in TABS"
            :key="tab"
            :label="t(`history.tab.${tab}`)"
            :name="tab"
          />
        </el-tabs>

        <!-- 筛选标签 -->
        <div class="filter-section">
          <el-check-tag
            v-for="key in store.legendKeys"
            :key="key"
            :checked="store.filters.includes(key)"
            @change="() => store.toggleFilter(key)"
          >
            {{ t(`history.legend.${key}`) }}
          </el-check-tag>
        </div>
      </div>
    </header>

    <!-- 内容区域 -->
    <main class="history-content">
      <template v-if="store.hasData">
        <section v-for="(group, date) in store.groupedMap" :key="date" class="date-group">
          <h3 class="date-header">{{ date }}</h3>

          <article class="transaction-list">
            <div v-for="item in group" :key="item.id" class="transaction-item">
              <!-- 图标 -->
              <div class="item-icon">
                <div class="icon-wrapper" :class="getStatusClass(item)">
                  <el-icon :size="20">
                    <component :is="getIcon(item)" />
                  </el-icon>
                </div>
              </div>

              <!-- 信息 -->
              <div class="item-info">
                <div class="info-title">
                  <span>{{ getTitle(item) }}</span>
                  <el-tag :type="getStatusType(item)" size="small" round>
                    {{ getStatusText(item) }}
                  </el-tag>
                </div>

                <div class="info-order">
                  <span class="order-text">{{ item.orderid }}</span>
                  <el-button text size="small" @click.stop="copyOrderId(item.orderid)">
                    {{ t('history.copy') }}
                  </el-button>
                </div>

                <div class="info-time">{{ item.time }}</div>

                <div v-if="getMemo(item)" class="info-memo">
                  {{ getMemo(item) }}
                </div>
              </div>

              <!-- 金额 -->
              <div class="item-amount">
                <template v-if="store.active === 'bet'">
                  <div class="bet-display">
                    <div class="bet-main">
                      <span
                        class="bet-result"
                        :style="betProfitStyle(item)"
                        :title="`${t(isWinItem(item) ? 'history.legend.win' : 'history.legend.loss')}: ${profitRate(item)}`"
                      >
                        <span class="result-symbol" :style="resultSymbolStyle">{{
                          isWinItem(item) ? '+' : '-'
                        }}</span>
                        {{ `$${Math.abs(profitAmount(item)).toFixed(2)}` }}
                      </span>
                    </div>
                    <div class="bet-details">
                      <span class="detail-item">
                        <span :style="betLabelStyle">{{ t('history.betAmount') }}: </span>
                        <span :style="betBetValueStyle">{{ `$${betAmount(item)}` }}</span>
                      </span>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <span
                    class="amount-value"
                    :style="getAmountStyle(getItemCoin(item), store.active)"
                  >
                    {{ formatSignedCurrency(getItemCoin(item), store.active === 'bonus') }}
                  </span>
                </template>
              </div>
            </div>
          </article>
        </section>
      </template>

      <el-empty v-else :description="t('history.empty')" />

      <!-- 加载更多触发器 -->
      <div ref="sentinel" class="sentinel">
        <div v-if="store.loading" class="loading-indicator">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>{{ t('history.loadMore') }}...</span>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import {
  Clock,
  CircleCheck,
  CircleClose,
  Download,
  Upload,
  Trophy,
  WarningFilled,
  Loading,
} from '@element-plus/icons-vue'
import type { HistoryItem } from '@/stores/history'
import { useAppBarStore } from '@/stores/appbar'
import { useHistoryStore } from '@/stores/history'
import {
  getStatusClass as _getStatusClass,
  getStatusType as _getStatusType,
  getStatusText as _getStatusText,
  isNegativeAmount,
} from '@/utils/status'
import type { HistoryTab as TabType, StatusType } from '@/utils/status'
import { formatSignedCurrency } from '@/utils/format'

// ========== 类型定义 ==========
type StatusKey = 'success' | 'fail' | 'pending' | 'win' | 'loss' | 'in' | 'out'

interface StatusConfig {
  readonly class: string
  readonly icon: any
}

interface AmountStyle {
  readonly color: string
  readonly fontSize: string
  readonly fontWeight: string
  readonly fontFamily: string
  readonly letterSpacing: string
}

// ========== 常量定义 ==========
const TABS: readonly TabType[] = ['deposit', 'withdraw', 'bet', 'bonus']

const STATUS_CONFIG: Readonly<Record<StatusKey, StatusConfig>> = {
  success: { class: 'status-success', icon: CircleCheck },
  fail: { class: 'status-fail', icon: CircleClose },
  pending: { class: 'status-pending', icon: Clock },
  win: { class: 'status-win', icon: Trophy },
  loss: { class: 'status-loss', icon: WarningFilled },
  in: { class: 'status-in', icon: Download },
  out: { class: 'status-out', icon: Upload },
}

// 样式常量
const AMOUNT_STYLES = {
  positive: '#52c41a',
  negative: '#ff4d4f',
  neutral: '#303133',
  base: {
    fontSize: '18px',
    fontWeight: '700',
    fontFamily: 'monospace',
    letterSpacing: '-0.5px',
  } as AmountStyle,
}

// ========== 组合式API ==========
const { t } = useI18n()
const store = useHistoryStore()
const appBarStore = useAppBarStore()
const sentinel = ref<HTMLDivElement>()

// ========== 生命周期 ==========
let observer: IntersectionObserver | null = null

onMounted(async () => {
  await initPage()
  await nextTick()
  setupInfiniteScroll()
})

onBeforeUnmount(() => {
  cleanupObserver()
})

watch(
  () => store.active,
  () => {
    handleTabChange()
    cleanupObserver()
    nextTick(() => setupInfiniteScroll())
  },
)

// ========== 初始化函数 ==========
async function initPage() {
  await store.fetchData(true)
  appBarStore.set({
    showBack: true,
    titleKey: 'page.history',
    supportUrl: store.data?.url,
  })
}

function setupInfiniteScroll() {
  if (!sentinel.value) return
  cleanupObserver()
  observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting && store.hasMore && !store.loading) {
          store.loadMore()
        }
      })
    },
    { root: null, rootMargin: '200px 0px', threshold: 0 },
  )
  observer.observe(sentinel.value)
}

function cleanupObserver() {
  if (observer && sentinel.value) {
    observer.unobserve(sentinel.value)
    observer.disconnect()
  }
}

function handleTabChange() {
  store.resetForTab()
  store.fetchData(true)
}

// ========== 金额与投注展示辅助 ==========

function getAmountStyle(coin: string | number | undefined, type: TabType) {
  const isBonusType = type === 'bonus'
  const color = isBonusType
    ? isNegativeAmount(coin)
      ? AMOUNT_STYLES.negative
      : AMOUNT_STYLES.positive
    : AMOUNT_STYLES.neutral
  return { ...AMOUNT_STYLES.base, color }
}

// 投注详情数值
function betAmount(item: any): number {
  return Number(item?.bet || 0)
}

function winAmount(item: any): number {
  return Number(item?.wincoin || 0)
}

function profitAmount(item: any): number {
  if (item.status === 2) {
    return Number(item?.wincoin)
  } else if (item.status === 3) {
    return Number(item?.bet) * -1
  }
  return 0
}

function profitRate(item: any): string {
  const bet = betAmount(item)
  const profit = profitAmount(item)
  const rate = bet > 0 ? (profit / bet) * 100 : 0
  return `${rate.toFixed(1)}%`
}

function isWinItem(item: any): boolean {
  // 按需求：status=2 为赢；status=3 为输
  return Number(item?.status) === 2
}

// 投注详情样式
function betProfitStyle(item: any) {
  const isWin = isWinItem(item)
  return {
    color: isWin ? AMOUNT_STYLES.positive : AMOUNT_STYLES.negative,
    fontSize: '20px',
    fontWeight: '800',
    display: 'inline-flex',
    alignItems: 'center',
    gap: '2px',
  }
}

const betLabelStyle = { opacity: '0.7', fontSize: '11px' }
const betBetValueStyle = {
  color: '#595959',
  fontWeight: '600',
  fontFamily: 'monospace',
  fontSize: '12px',
}
function betWinValueStyle(item: any) {
  const win = winAmount(item)
  return {
    color: win > 0 ? AMOUNT_STYLES.positive : AMOUNT_STYLES.negative,
    fontWeight: win > 0 ? '700' : '600',
    opacity: win > 0 ? '1' : '0.8',
    fontFamily: 'monospace',
    fontSize: '12px',
  }
}
const betSeparatorStyle = { opacity: '0.4', fontSize: '10px' }
const resultSymbolStyle = { fontSize: '16px', fontWeight: '700' }

// ========== 业务逻辑函数 ==========
const titleStrategies: Record<TabType, (item: any) => string> = {
  bonus: (item) => item.status_str || 'Bonus',
  bet: (item) => item.title || 'Bet',
  deposit: (item) => item.title || 'Deposit',
  withdraw: () => 'Withdraw',
}

function getTitle(item: HistoryItem): string {
  const strategy = titleStrategies[store.active as TabType]
  return strategy?.(item as any) || 'Transaction'
}

function getIcon(item: HistoryItem) {
  const statusClass = getStatusClass(item)
  const key = statusClass.replace('status-', '') as StatusKey
  return STATUS_CONFIG[key]?.icon || Clock
}

function getStatusType(item: HistoryItem): StatusType {
  return _getStatusType(store.active as TabType, item)
}

function getStatusText(item: HistoryItem): string {
  return _getStatusText(store.active as TabType, item, t)
}

function getStatusClass(item: HistoryItem): string {
  return _getStatusClass(store.active as TabType, item)
}
function getMemo(item: HistoryItem): string {
  const memo = (item as any).memo
  if (!memo) return ''

  const isWithdrawRejected = store.active === 'withdraw' && Number((item as any).status) === 3

  return isWithdrawRejected ? `Reason: ${memo}` : memo
}

// 统一获取 coin 字段，容错字符串/数字
function getItemCoin(item: any): string | number | undefined {
  return (item && (item.coin as any)) ?? undefined
}

async function copyOrderId(orderId: string) {
  try {
    await navigator.clipboard.writeText(orderId)
    ElMessage.success(t('history.copied'))
  } catch {
    ElMessage.error('Copy failed')
  }
}

//（已移除 TransactionAmount 组件，逻辑已内联到 template）
</script>

<style scoped lang="scss">
// ========== CSS变量定义 ==========
.history-page {
  // 布局变量
  --header-height: 56px;
  --spacing: 12px;
  --radius: 8px;

  // 颜色变量
  --color-success: #52c41a;
  --color-danger: #ff4d4f;
  --color-warning: #faad14;
  --color-info: #1890ff;
  --color-neutral: #303133;

  // 背景渐变
  --bg-success: linear-gradient(135deg, #f6ffed, #e6ffed);
  --bg-danger: linear-gradient(135deg, #fff2f0, #ffebe6);
  --bg-warning: linear-gradient(135deg, #fffbe6, #fff7e6);
  --bg-info: linear-gradient(135deg, #e6f7ff, #e6f7ff);

  // 阴影
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 2px 8px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 2px 12px rgba(0, 0, 0, 0.12);

  // 动画
  --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.15s ease-out;
}

// ========== 布局 ==========
.history-page {
  margin: 0 auto;
  height: calc(100vh - var(--header-height));
  display: flex;
  flex-direction: column;
}

// ========== 固定头部 ==========
.history-header {
  margin: 0 auto;
  padding: 2px;
  position: fixed;
  top: var(--header-height);
  left: 0;
  right: 0;
  z-index: 100;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-lighter);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.header-container {
  margin: 0 auto;
}

// Tab样式
.history-tabs {
  :deep(.el-tabs__header) {
    margin-bottom: 8px;
  }

  :deep(.el-tabs__nav-wrap::after) {
    height: 1px;
  }

  :deep(.el-tabs__item) {
    font-size: 14px;
    padding: 0 16px;
    height: 40px;
    line-height: 40px;

    &.is-active {
      color: var(--el-color-primary);
      font-weight: 600;
    }
  }

  :deep(.el-tabs__active-bar) {
    height: 2px;
    background: var(--el-color-primary);
  }
}

// 筛选区
.filter-section {
  display: flex;
  justify-content: center;
  gap: 8px;
  padding: 0 var(--spacing);
  flex-wrap: wrap;

  .el-check-tag {
    font-size: 12px;
    padding: 5px 12px;
    border-radius: 14px;
    transition: var(--transition);

    &:hover:not(.is-checked) {
      border-color: var(--el-color-primary-light-5);
      background: var(--el-color-primary-light-9);
    }

    &.is-checked {
      background: var(--el-color-primary);
      border-color: var(--el-color-primary);
      color: white;
      font-weight: 500;
    }
  }
}

// 提示
.tip-alert {
  :deep(.el-alert__title) {
    font-size: 12px;
  }
}

// ========== 内容区 ==========
.history-content {
  flex: 1;
  overflow-y: auto;
  padding-top: 140px; // 为固定头部留空间
  background: linear-gradient(to bottom, #f8f9fa, #f5f6f7);

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.15);
    border-radius: 3px;

    &:hover {
      background: rgba(0, 0, 0, 0.25);
    }
  }
}

// 日期分组
.date-group {
  margin-bottom: var(--spacing);

  &:last-child {
    margin-bottom: 0;
  }
}

.date-header {
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 500;
  color: var(--el-text-color-secondary);
  background: linear-gradient(to right, #f0f2f5, #f5f7fa);
  border-left: 3px solid var(--el-color-primary-light-5);
  margin: 0;
}

// 交易列表
.transaction-list {
  background: white;
  border-radius: var(--radius);
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.transaction-item {
  display: flex;
  align-items: center;
  padding: var(--spacing) 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  transition: var(--transition);
  cursor: pointer;

  &:hover {
    background: linear-gradient(to right, #fafbfc, #ffffff);
    transform: translateX(2px);
  }

  &:last-child {
    border-bottom: none;
  }
}

// 图标
.item-icon {
  flex-shrink: 0;
  margin-right: var(--spacing);
}

.icon-wrapper {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);

  // 状态样式 - 成功/进入
  &.status-success,
  &.status-in {
    background: var(--bg-success);
    color: var(--color-success);
    box-shadow: var(--shadow-md);
  }

  // 状态样式 - 失败/退出
  &.status-fail,
  &.status-out {
    background: var(--bg-danger);
    color: var(--color-danger);
    box-shadow: var(--shadow-md);
  }

  // 状态样式 - 等待中
  &.status-pending {
    background: var(--bg-warning);
    color: var(--color-warning);
    box-shadow: var(--shadow-md);
  }

  // 状态样式 - 赢
  &.status-win {
    background: var(--bg-warning);
    color: var(--color-warning);
    box-shadow: var(--shadow-lg);
  }

  // 状态样式 - 输
  &.status-loss {
    background: linear-gradient(135deg, #f5f5f5, #e8e8e8);
    color: #8c8c8c;
    box-shadow: var(--shadow-sm);
  }

  // 特殊效果：赢的动画
  &.status-win {
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      inset: -50%;
      background: radial-gradient(circle, rgba(255, 215, 0, 0.2) 0%, transparent 70%);
      animation: pulse 2s ease-in-out infinite;
    }

    .el-icon {
      position: relative;
      z-index: 1;
    }
  }

  // 特殊效果：输的透明度
  &.status-loss {
    opacity: 0.85;
  }
}

// 信息区
.item-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);

  .el-tag {
    font-size: 11px;
    padding: 0 6px;
    height: 18px;
  }
}

.info-order {
  display: flex;
  align-items: center;
  gap: 8px;

  .order-text {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    font-family: monospace;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .el-button {
    padding: 0 4px;
    height: 18px;
    font-size: 11px;
  }
}

.info-time {
  font-size: 11px;
  color: var(--el-text-color-placeholder);
}

.info-memo {
  width: fit-content;
  margin-top: 4px;
  padding: 2px 6px;
  background: #fff7e6;
  color: #fa8c16;
  font-size: 11px;
  border-radius: 4px;
  border-left: 2px solid #ffd8bf;
}

// 金额
.item-amount {
  flex-shrink: 0;
  margin-left: var(--spacing);
  text-align: right;
  display: flex;
  align-items: center;
}

.amount-value {
  font-size: 18px;
  font-weight: 700;
  font-family: 'SF Mono', Monaco, Consolas, monospace;
  letter-spacing: -0.5px;

  &.amount-positive {
    color: var(--color-success);
  }

  &.amount-negative {
    color: var(--color-danger);
  }
}

// ========== 投注显示优化 ==========
.bet-display {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  min-width: 120px;
  position: relative;

  // 主要盈亏显示
  .bet-main {
    .bet-result {
      cursor: help;
      transition: transform 0.2s;
      font-family: 'SF Mono', Monaco, Consolas, monospace;
      letter-spacing: -0.5px;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  // 详细信息
  .bet-details {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 11px;
    color: var(--el-text-color-secondary);

    .detail-item {
      display: flex;
      align-items: center;
      gap: 2px;
    }
  }
}

// 微光动画
@keyframes shimmer {
  0%,
  100% {
    opacity: 0.3;
    transform: translateX(-100%);
  }
  50% {
    opacity: 0.6;
    transform: translateX(100%);
  }
}

// 脉冲动画
@keyframes pulse {
  0%,
  100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

.sentinel {
  min-height: 40px;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-secondary);
  font-size: 14px;

  .is-loading {
    animation: rotating 2s linear infinite;
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.el-empty {
  padding: 48px 0;
}

// ========== 响应式 ==========
@media (max-width: 768px) {
  .history-page {
    --spacing: 8px;
    --radius: 6px;
  }

  .header-container {
    padding-bottom: 6px;
  }

  .filter-section {
    padding: 0 8px;
  }

  .transaction-item {
    padding: 10px 12px;
  }

  .icon-wrapper {
    width: 36px;
    height: 36px;
  }

  .amount-value {
    font-size: 15px;
  }

  .info-title {
    font-size: 13px;
  }

  .info-order .order-text {
    font-size: 11px;
  }
}
</style>
