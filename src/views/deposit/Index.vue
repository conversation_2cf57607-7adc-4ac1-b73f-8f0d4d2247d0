<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="deposit-page">
    <section class="balance-banner">
      <div class="balance-title">{{ t('deposit.currentBalance') }}</div>
      <div class="balance-amount">{{ formatCurrency(balanceAmount) }}</div>
    </section>
    <el-card shadow="never" class="block">
      <div class="amount-header">
        <AmountInput
          v-model="store.amount"
          :min="0"
          :step="10"
          :title="t('deposit.selectamount')"
          :width="260"
        />
      </div>

      <div class="quick-amounts" role="list">
        <el-button
          v-for="coin in coins"
          :key="coin"
          :type="store.amount === coin ? 'primary' : undefined"
          :plain="store.amount !== coin"
          round
          size="large"
          role="listitem"
          :aria-pressed="store.amount === coin"
          @click="store.amount = coin"
          @keydown.enter.prevent="store.amount = coin"
          >{{ coin }}</el-button
        >
      </div>
    </el-card>

    <el-card shadow="never" class="block">
      <h3 class="block-title">{{ t('deposit.paymentMethod') }}</h3>
      <div v-if="store.methods.length" class="cards methods">
        <button
          v-for="m in store.methods"
          :key="m.id"
          class="pill"
          :class="{ active: m.id === store.selectedMethodId }"
          @click="store.selectMethod(m.id)"
        >
          <span class="pill-title">{{ m.title }}</span>
          <span v-if="m.disrate" class="pill-badge">{{ m.disrate }}</span>
        </button>
      </div>
      <el-empty v-else :description="t('deposit.noMethods')" />
    </el-card>

    <el-card shadow="never" class="block">
      <h3 class="block-title">{{ t('deposit.paymentChannel') }}</h3>
      <div class="cards channels" role="list">
        <button
          v-for="p in store.pages"
          :key="p.id"
          class="pill"
          :class="{ active: p.id === store.selectedPageId }"
          role="listitem"
          :aria-selected="p.id === store.selectedPageId"
          @click="store.selectPage(p.id)"
          @keydown.enter.prevent="store.selectPage(p.id)"
        >
          <span class="pill-title">{{ p.title }}</span>
          <span v-if="p.disrate" class="pill-badge">{{ p.disrate }}</span>
        </button>
      </div>

      <el-alert
        v-if="!canSubmit && mincoin !== null && maxcoin !== null"
        :title="outOfRangeText"
        type="warning"
        :closable="false"
        class="limit-hint"
        show-icon
      />

      <div class="info-panel">
        <div class="limit-line" v-if="limitText">{{ limitText }}</div>
        <div class="money-row">
          <span>{{ t('deposit.cashBalance') }}</span>
          <strong>{{ formatCurrency(cashBalance) }}</strong>
        </div>
        <div class="money-row">
          <span>{{ t('deposit.cashBonus') }}</span>
          <strong>{{ formatCurrency(cashBonus) }}</strong>
        </div>
      </div>
    </el-card>

    <div class="cta">
      <el-button
        type="success"
        size="large"
        :loading="store.loading"
        :disabled="!canSubmit"
        @click="onSubmit"
      >
        {{ submitText }}
      </el-button>
    </div>

    <el-card shadow="never" class="block">
      <h4 class="tips-title">{{ t('common.tips') }}</h4>
      <ul class="tips-list">
        <li v-for="(line, idx) in tips" :key="idx">{{ line }}</li>
      </ul>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed, watch } from 'vue'
import AmountInput from '@/components/AmountInput.vue'
import { useI18n } from 'vue-i18n'
import { useAppBarStore } from '@/stores/appbar'
import { useDepositStore } from '@/stores/deposit'
import { formatCurrency, htmlBrToLines, inRange } from '@/utils/format'
import { computeCashBonus } from '@/utils/reward'
import { useRouter } from 'vue-router'

const { t } = useI18n()
const appBarStore = useAppBarStore()
const store = useDepositStore()
const defaultCoins = [10, 50, 100, 300, 1000, 3000, 5000, 10000]
const coins = computed(() => store.data?.coins ?? defaultCoins)

onMounted(() => {
  store.refresh()
  appBarStore.set({
    titleKey: 'page.deposit',
    showBack: true,
    supportUrl: store.data?.url,
  })
})

// Debounce 刷新余额
let timer: number | null = null
watch(
  () => store.amount,
  () => {
    if (timer) window.clearTimeout(timer)
    timer = window.setTimeout(() => store.refresh(), 300)
  },
)

const tips = computed(() => htmlBrToLines(store.data?.memo))
const balanceAmount = computed(() => store.data?.user.coin ?? 0)
const mincoin = computed(() => store.selectedPage?.mincoin ?? store.selectedMethod?.mincoin ?? null)
const maxcoin = computed(() => store.selectedPage?.maxcoin ?? store.selectedMethod?.maxcoin ?? null)
const cashBalance = computed(() => Number(store.amount) || 0)
const cashBonus = computed(() => computeCashBonus(cashBalance.value, store.selectedPage?.disrate))

const limitText = computed(() =>
  mincoin.value !== null && maxcoin.value !== null
    ? t('deposit.limitHint', {
        min: formatCurrency(mincoin.value || 0),
        max: formatCurrency(maxcoin.value || 0),
      })
    : '',
)
const outOfRangeText = computed(() =>
  mincoin.value !== null && maxcoin.value !== null
    ? t('deposit.outOfRange', {
        min: formatCurrency(mincoin.value || 0),
        max: formatCurrency(maxcoin.value || 0),
      })
    : '',
)
const submitText = computed(() =>
  t('deposit.submitAmount', { amount: formatCurrency(Number(store.amount) || 0) }),
)

const canSubmit = computed(() => {
  const min = mincoin.value ?? 0
  const max = maxcoin.value ?? Number.MAX_SAFE_INTEGER
  return !!store.selectedMethodId && inRange(Number(store.amount), min, max)
})

const router = useRouter()

async function onSubmit() {
  if (!canSubmit.value) return
  const res = await store.submit()
  router.push({
    path: '/cashier',
    query: { ...res },
  })
}
</script>

<style scoped lang="scss">
.balance-banner {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
  padding: 12px 12px 8px;
}
.balance-title {
  color: var(--el-text-color-secondary);
  font-size: 13px;
}
.balance-amount {
  font-size: 20px;
  font-weight: 700;
}

.block {
  margin-bottom: 12px;
}
.amount-input {
  display: flex;
  align-items: center;
  gap: 8px;
}
.amount-prefix {
  font-size: 16px;
  font-weight: 700;
  color: var(--el-text-color-regular);
}
.quick-amounts {
  margin-top: 12px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
}
/* 修正 Element Plus 默认的相邻按钮 margin-left，确保每个网格项等宽 */
.quick-amounts :deep(.el-button) {
  width: 100%;
  margin-left: 0;
}

.block-title {
  margin: 0 0 10px 0;
  border-left: 3px solid #20a37a;
  padding-left: 8px;
}
.amount-header {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
  gap: 8px;
}
.range-hint {
  color: var(--el-text-color-secondary);
  font-size: 12px;
}
.cards {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 12px;
}
.methods {
  margin-top: 6px;
}
.channels {
  margin-top: 6px;
}

.pill {
  position: relative;
  appearance: none;
  border: 1px solid var(--el-border-color);
  background: var(--el-bg-color);
  border-radius: 14px;
  min-height: 56px;
  padding: 12px 14px;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
}
.pill:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}
.pill.active {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}
.pill-title {
  font-weight: 600;
}
.pill-badge {
  position: absolute;
  right: 10px;
  top: 8px;
  font-size: 12px;
  color: #fff;
  background: #ff7a00;
  border-radius: 10px;
  padding: 2px 6px;
}

.limit-hint {
  margin-top: 10px;
}
.info-panel {
  margin-top: 12px;
  background: var(--el-fill-color-light);
  border-radius: 10px;
  padding: 10px 12px;
  border: 1px solid var(--el-border-color);
}
.limit-line {
  color: var(--el-text-color-secondary);
  font-size: 12px;
  margin-bottom: 6px;
}
.money-row {
  display: flex;
  justify-content: space-between;
  padding: 6px 0;
}
.cta {
  position: sticky;
  bottom: 12px;
  padding: 8px 0;
  background: transparent;
  display: flex;
  justify-content: center;
}

@media (max-width: 480px) {
  .summary {
    grid-template-columns: 1fr;
  }
  .cards {
    grid-template-columns: 1fr;
  }
  .deposit-page {
    padding: 2px;
  }
  .quick-amounts {
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
  }
  .pill {
    border-radius: 16px;
    min-height: 60px;
    padding: 14px;
  }
  .cta {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 12px;
    background: linear-gradient(180deg, transparent, rgba(247, 248, 250, 0.96));
  }
}
</style>
