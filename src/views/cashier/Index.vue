<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="cashier">
    <div class="payment-container">
      <div class="crypto-icon">{{ cashier.state.token }}</div>
      <h1 class="payment-title">{{ t('cashier.title') }}</h1>
      <p class="payment-subtitle">
        <span class="status-indicator"></span>
        {{ t('cashier.network', { chain: cashier.state.chain }) }}
      </p>

      <div class="order-info">
        <div class="order-item">
          <span class="order-label">{{ t('cashier.amount') }}:</span>
          <span class="order-value amount-highlight" id="payAmount"
            >{{ cashier.state.actual_amount }} {{ cashier.state.token }}</span
          >
        </div>
        <div class="order-item">
          <span class="order-label">{{ t('cashier.orderNo') }}:</span>
          <span class="order-value" id="orderNumber">{{ cashier.state.order_id }}</span>
        </div>
        <div class="order-item">
          <span class="order-label">{{ t('cashier.tradeId') }}:</span>
          <span class="order-value" id="tradeId">{{ cashier.state.trade_id }}</span>
        </div>
      </div>

      <div class="countdown-banner">
        <div class="countdown-content">
          <div class="countdown-info">
            <div class="countdown-title">⏰ {{ t('cashier.countdown') }}</div>
            <div class="countdown-subtitle">{{ t('cashier.scanTip') }}</div>
          </div>
          <div class="countdown-timer" id="countdown">
            <div class="time-unit">
              <span class="time-number" id="minutes">{{ mm }}</span>
              <span class="time-label">{{ t('cashier.unitMin') }}</span>
            </div>
            <span class="time-separator">:</span>
            <div class="time-unit">
              <span class="time-number" id="seconds">{{ ss }}</span>
              <span class="time-label">{{ t('cashier.unitSec') }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="qr-section">
        <div class="qr-code" id="qrcode">
          <QrcodeCanvas :text="address" :size="140" />
        </div>
        <div class="address-section">
          <div class="address-label">
            {{ t('cashier.addressLabel', { chain: cashier.state.chain }) }}
          </div>
          <div class="address-row">
            <div class="address-text" id="walletAddress">{{ address }}</div>
            <button class="copy-btn" @click="copyAddress">{{ t('cashier.copyAddress') }}</button>
          </div>
        </div>
      </div>

      <div class="instructions">
        <h4>📋 {{ t('cashier.noticeTitle') }}</h4>
        <ol>
          <li v-for="(tip, i) in tips" :key="i">{{ tip }}</li>
        </ol>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onBeforeUnmount } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { useCashierStore } from '@/stores/cashier'
import QrcodeCanvas from '@/components/QrcodeCanvas.vue'
import { useCountdown } from '@/composables/useCountdown'
import { type DepositResult } from '@/types/api/deposit'

const { t, tm } = useI18n()
const route = useRoute()
const cashier = useCashierStore()

cashier.setup(route.query as DepositResult)

const address = computed(() => cashier.state.address || '')
const tips = computed<string[]>(() => {
  const v = tm('cashier.tips')
  return Array.isArray(v) ? (v as string[]) : []
})

const { mm, ss } = useCountdown(Number(cashier.state.expiration_time || 0), cashier.onExpired)

onMounted(() => {
  cashier.startPolling()
})

onBeforeUnmount(() => {
  cashier.stopPolling()
})

async function copyAddress() {
  if (!address.value) return
  await navigator.clipboard.writeText(address.value)
  ElMessage.success(t('cashier.copied'))
}
</script>

<style scoped lang="scss">
.cashier {
  min-height: 100vh;
  padding: 12px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}
.payment-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  width: 100%;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.crypto-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 15px;
  background: linear-gradient(45deg, #26a17b, #38cf91);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #fff;
  font-weight: bold;
  box-shadow: 0 6px 15px rgba(38, 161, 123, 0.3);
}
.payment-title {
  font-size: 22px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
}
.payment-subtitle {
  color: #718096;
  font-size: 14px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}
.status-indicator {
  width: 10px;
  height: 10px;
  background: #48bb78;
  border-radius: 50%;
  display: inline-block;
  animation: pulse 2s infinite;
}
.network-badge {
  display: inline-block;
  background: #48bb78;
  color: #fff;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  margin-left: 6px;
}
.order-info {
  background: #f7fafc;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 18px;
  border-left: 3px solid #667eea;
}
.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 14px;
}
.order-item:last-child {
  margin-bottom: 0;
}
.order-label {
  color: #4a5568;
  font-weight: 500;
}
.order-value {
  color: #2d3748;
  font-weight: 600;
}
.amount-highlight {
  color: #e53e3e;
  font-size: 16px;
  font-weight: 700;
}
.countdown-banner {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 18px;
  position: relative;
  overflow: hidden;
}
.countdown-content {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
}
.countdown-info {
  text-align: left;
}
.countdown-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 3px;
  opacity: 0.9;
}
.countdown-subtitle {
  font-size: 12px;
  opacity: 0.8;
}
.countdown-timer {
  display: flex;
  gap: 8px;
  align-items: center;
}
.time-unit {
  text-align: center;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 6px;
  padding: 6px 10px;
  min-width: 40px;
}
.time-number {
  font-size: 16px;
  font-weight: 700;
  display: block;
  line-height: 1;
}
.time-label {
  font-size: 9px;
  opacity: 0.8;
  margin-top: 2px;
}
.time-separator {
  font-size: 16px;
  font-weight: 700;
  opacity: 0.8;
  animation: blink 1s infinite;
}
.qr-section {
  background: #fff;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}
.qr-code {
  width: 160px;
  height: 160px;
  margin: 0 auto;
  border: 2px solid #e2e8f0;
  border-radius: 6px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}
.address-section {
  background: #f7fafc;
  border-radius: 6px;
  padding: 12px;
  margin-top: 4px;
  text-align: left;
  width: 100%;
}
.address-row {
  display: flex;
  gap: 8px;
  align-items: center;
}
.address-row .address-text {
  flex: 1;
  height: 16px;
}

.address-label {
  color: #4a5568;
  font-size: 12px;
  margin-bottom: 6px;
}
.address-text {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 8px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 10px;
  word-break: break-all;
  color: #2d3748;
}
.copy-btn {
  background: #667eea;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}
.copy-btn:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}
.instructions {
  background: #fff5cd;
  border: 1px solid #f6e05e;
  border-radius: 6px;
  padding: 12px;
  text-align: left;
}
.instructions h4 {
  color: #744210;
  font-size: 14px;
  margin-bottom: 8px;
}
.instructions ol {
  color: #744210;
  font-size: 12px;
  padding-left: 16px;
}
.instructions li {
  margin-bottom: 3px;
}

@keyframes blink {
  0%,
  50% {
    opacity: 0.8;
  }
  51%,
  100% {
    opacity: 0.3;
  }
}
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .payment-container {
    padding: 20px;
    max-width: 400px;
  }
  .crypto-icon {
    width: 50px;
    height: 50px;
    font-size: 12px;
    margin-bottom: 12px;
  }
  .payment-title {
    font-size: 20px;
  }
  .qr-code {
    width: 140px;
    height: 140px;
  }
  .countdown-content {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
  .countdown-info {
    text-align: center;
  }
}
</style>
