import { ref, onBeforeUnmount } from 'vue'

export function useCountdown(initial: number, onFinish?: () => void) {
  const mm = ref('00')
  const ss = ref('00')
  let timer: number | null = null

  function start(total: number) {
    let left = Math.max(0, total)
    const tick = () => {
      const m = Math.floor(left / 60)
      const s = left % 60
      mm.value = String(m).padStart(2, '0')
      ss.value = String(s).padStart(2, '0')
      if (left <= 0) {
        onFinish && onFinish()
        return
      }
      left -= 1
      timer = window.setTimeout(tick, 1000)
    }
    tick()
  }

  onBeforeUnmount(() => {
    if (timer) window.clearTimeout(timer)
  })

  if (initial > 0) start(initial)

  return { mm, ss, start }
}
