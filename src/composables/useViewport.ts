import { ref, computed, onMounted, onBeforeUnmount } from 'vue'

export interface ViewportConfig {
  // 字体大小配置
  baseFontSize: {
    mobile: number    // 移动端基础字体大小 (px)
    tablet: number    // 平板端基础字体大小 (px)
    desktop: number   // 桌面端基础字体大小 (px)
  }
  // Element Plus 组件大小
  componentSize: 'small' | 'default' | 'large'
  // 断点配置
  breakpoints: {
    mobile: number    // 移动端断点
    tablet: number    // 平板端断点
  }
}

const defaultConfig: ViewportConfig = {
  baseFontSize: {
    mobile: 16,      // 移动端字体更大
    tablet: 15,
    desktop: 14
  },
  componentSize: 'large',
  breakpoints: {
    mobile: 768,
    tablet: 1024
  }
}

export function useViewport(config: Partial<ViewportConfig> = {}) {
  const finalConfig = { ...defaultConfig, ...config }
  
  const windowWidth = ref(0)
  const windowHeight = ref(0)
  
  // 设备类型判断
  const isMobile = computed(() => windowWidth.value < finalConfig.breakpoints.mobile)
  const isTablet = computed(() => 
    windowWidth.value >= finalConfig.breakpoints.mobile && 
    windowWidth.value < finalConfig.breakpoints.tablet
  )
  const isDesktop = computed(() => windowWidth.value >= finalConfig.breakpoints.tablet)
  
  // 当前设备类型
  const deviceType = computed(() => {
    if (isMobile.value) return 'mobile'
    if (isTablet.value) return 'tablet'
    return 'desktop'
  })
  
  // 动态字体大小
  const fontSize = computed(() => {
    const { baseFontSize } = finalConfig
    if (isMobile.value) return baseFontSize.mobile
    if (isTablet.value) return baseFontSize.tablet
    return baseFontSize.desktop
  })
  
  // Element Plus 组件大小
  const componentSize = computed(() => {
    if (isMobile.value) return 'large'
    return finalConfig.componentSize
  })
  
  // CSS 变量更新
  const updateCSSVariables = () => {
    const root = document.documentElement
    const currentFontSize = fontSize.value
    
    // 更新字体相关变量
    root.style.setProperty('--app-font-size', `${currentFontSize}px`)
    root.style.setProperty('--el-font-size-base', `${currentFontSize}px`)
    root.style.setProperty('--el-font-size-small', `${currentFontSize - 2}px`)
    root.style.setProperty('--el-font-size-large', `${currentFontSize + 2}px`)
    
    // 移动端特殊处理
    if (isMobile.value) {
      // 增大按钮最小高度
      root.style.setProperty('--el-component-size', '44px')
      root.style.setProperty('--el-component-size-large', '48px')
      
      // 增大输入框内边距
      root.style.setProperty('--el-input-height', '44px')
      root.style.setProperty('--el-input-height-large', '48px')
      
      // 增大间距
      root.style.setProperty('--app-spacing', '16px')
      root.style.setProperty('--app-spacing-large', '24px')
    } else {
      // 恢复默认值
      root.style.setProperty('--el-component-size', '32px')
      root.style.setProperty('--el-component-size-large', '40px')
      root.style.setProperty('--el-input-height', '32px')
      root.style.setProperty('--el-input-height-large', '40px')
      root.style.setProperty('--app-spacing', '12px')
      root.style.setProperty('--app-spacing-large', '16px')
    }
  }
  
  // 窗口大小更新
  const updateWindowSize = () => {
    windowWidth.value = window.innerWidth
    windowHeight.value = window.innerHeight
    updateCSSVariables()
  }
  
  // 生命周期
  onMounted(() => {
    updateWindowSize()
    window.addEventListener('resize', updateWindowSize)
  })
  
  onBeforeUnmount(() => {
    window.removeEventListener('resize', updateWindowSize)
  })
  
  return {
    // 响应式数据
    windowWidth,
    windowHeight,
    isMobile,
    isTablet,
    isDesktop,
    deviceType,
    fontSize,
    componentSize,
    
    // 方法
    updateCSSVariables,
    
    // 配置
    config: finalConfig
  }
}
