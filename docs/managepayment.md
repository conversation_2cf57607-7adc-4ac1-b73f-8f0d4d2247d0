# 支付管理页面分析（/pages/deposit/managepayment/managepayment）

目标： http://paysms.haiwailaba.cyou/#/pages/deposit/managepayment/managepayment

## 1. 页面结构分析

- 顶部：Manage Payments 标题
- 模块：
  - My Bank Accounts（我的银行卡）
    - 卡片1：显示掩码账号 + VERIFIED + 右侧操作图标
    - 卡片2：Verify New Bank Account + Add（添加入口）
  - My Wallets UPI ID（我的钱包 UPI）
    - Paytm / Phonepe / Gpay / Other UPI ID 四个入口卡，各含 Logo + Link 按钮 + 右侧操作图标

- 样式与类（完整/节选）：
  - 页面/容器：uni-body, pages-deposit-managepayment-managepayment, uni-app--maxwidth, content, uni-navbar, uni-dark, uni-nvue-fixed, uni-navbar**content, uni-navbar--fixed, uni-navbar--border, uni-status-bar, uni-navbar**header, uni-navbar**header-btns, uni-navbar**header-btns-left, uni-navbar**content_view, uni-icons, uniui-left, uni-navbar**header-container, uni-navbar**header-container-inner, uni-nav-bar-text, uni-ellipsis-1, uni-navbar**header-btns-right, customimg, uni-navbar**placeholder, uni-navbar**placeholder-view, payments, title, uni-list, uni-border-top-bottom, uni-list--border-top, my-list-item, border--left, uni-list--border, my-list-item**container, container--right, slot-image, my-list-item**content, my-list-item**content-title, my-list-item**content-note, uni-icon-wrapper, uniui-arrowright, item, item-text, my-list-item**extra, my-list-item**extra-text, uni-list--border-bottom, upi-actionsheet, upi-actionsheet-tips, content-clear-icon, uniui-close, tips-txt, upi-operate-box, amount-wrap, amount, uni-easyinput, uni-input, uni-easyinput**content, uni-easyinput**content-input, uni-input-wrapper, uni-input-placeholder, uni-easyinput**placeholder-class, uni-input-input, uniui-false, upi-actionsheet-btn, upi-actionsheet-cancel, sure-btn, upi-actionsheet-mask, uni-mask, uni-actionsheet**mask, uni-actionsheet, uni-actionsheet**menu, uni-actionsheet**action, uni-actionsheet**cell, uni-modal, uni-modal**bd, uni-modal**ft, uni-modal**btn, uni-modal**btn_default, uni-modal**btn_primary
  - data-\*：data-page, data-v-34c9079d, data-v-5725e711, data-v-dc21f774, data-v-90ccf07e, data-v-0a75b799, data-v-e07ee5ea, data-v-1b9ee996, data-v-3c003be1, data-v-75f84caa
  - ID：无

- 布局：移动端卡片式列表，左右对齐元素依靠 flex 布局

- 交互元素：
  - “Verify New Bank Account / Add” 点击进入添加/验证流程
  - 每张卡右侧图标可能是进入详情/解绑
  - UPI 卡片的 Link 按钮触发绑定流程

## 2. 功能流程分析

```mermaid
flowchart TD
  A[进入页面] --> B[GET /user/banks?split=1]
  B --> C{是否已有银行卡}
  C -->|是| D[展示掩码与状态]
  C -->|否| E[引导添加]
  D --> F[管理：解绑/设默认]
  E --> G[跳转到添加银行账户]
```

- 页面状态：
  - 根据 /user/banks?split=1 渲染银行账户与 UPI 绑定状态
  - 点击 Link/添加等进入相应流程

- 验证：
  - 无表单，仅流程导航

- 路由：
  - 跳转到 /pages/deposit/addcreditcard/addcreditcard 等

## 3. API 接口逆向工程

- GET http://service.haiwailaba.cyou/user/banks?split=1
  - 用途：返回银行卡列表与UPI绑定情况
  - 请求头：匿名
  - 响应：200 JSON

- 其他：
  - 操作类接口（解绑、设默认、绑定UPI）未直接触发到

## 接口数据结构

### 1) GET /user/banks?split=1

- JSON 示例（真实抓取，已脱敏精简）：

```
{
  "code":0,
  "msg":"succ",
  "data":{
    "banks":[{"id":4,"name":"VERIFIED","checked":true,"status":2,"card":"12************12","value":"4"}],
    "upis":{
      "1":{"id":1,"name":"Paytm","checked":true,"card":"Link","icon":"/static/img/paytm.png","cat":1},
      "2":{"id":1,"name":"Phonepe","checked":true,"card":"Link","icon":"/static/img/phonepe.png","cat":2},
      "3":{"id":1,"name":"Gpay","checked":true,"card":"Link","icon":"/static/img/gpay.png","cat":3},
      "4":{"id":1,"name":"Other UPI ID","checked":true,"card":"Link","icon":"/static/img/upi.png","cat":4}
    },
    "kyc":1,
    "url":"https://vm.providesupport.com/04qet..."
  }
}
```

- 字段说明：
  - banks:array 已绑定银行卡（同 /user/banks）
  - upis:object UPI 绑定入口汇总（键为类别序号字符串）
    - 每项：id/name/checked/card/icon/cat
  - kyc:number 实名状态
- 数据驱动渲染：
  - “My Bank Accounts” 区域使用 banks 渲染卡片；为空时显示“Verify New Bank Account / Add”入口
  - “My Wallets UPI ID” 区域按 upis 的键顺序渲染四个 UPI 卡片，按钮文案来自 card（Link）
- 关键状态字段：
  - banks[].status/name/checked，upis[*].checked

## 4. 数据流分析

- 数据获取：/user/banks?split=1
- 状态：卡片列表的渲染状态（已绑定/未绑定）
- 刷新：操作完成后重新拉取
- 实时：无

## 克隆实现要点

- 组件：银行卡卡片、UPI 卡片
- API：查询接口与后续绑定/解绑接口预留
- 交互：跳转添加页、操作结果 Toast
