# 提现页面分析（/pages/withdraw/index）

目标： http://paysms.haiwailaba.cyou/#/pages/withdraw/index

## 1. 页面结构分析

- 标题：WITHDRAW
- 主要模块：
  - 顶部导航（返回、标题、右侧图标）
  - 可提现余额卡：Withdrawable Balance，数值 ₹1540
  - 账户列表区域：
    - 卡片1：Add New Bank Account（添加新银行账户）
    - 卡片2：VERIFIED 12\***\*\*\*\*\*\*\***12（已验证的尾号掩码账户）
  - 金额输入区：
    - 标签 Amount
    - 输入：带 ₹ 前缀的 number 输入（spinbutton，默认 0）
    - 限额提示：Withdraw min :₹0 & max:₹1000000 allowed each time
    - 提交按钮：WITHDRAW NOW
    - Tips 区：例如 bbbbdddd、awerqwerqwer
  - 错误/状态提示：无法连接到服务器~（当接口失败时展示）

- 主要 CSS 类与属性（完整/节选）：
  - 页面/容器：uni-body, pages-withdraw-index, uni-app--maxwidth, content, uni-navbar, uni-dark, uni-nvue-fixed, uni-navbar**content, uni-navbar--fixed, uni-navbar--border, uni-status-bar, uni-navbar**header, uni-navbar**header-btns, uni-navbar**header-btns-left, uni-navbar**content_view, uni-navbar**header-container, uni-navbar**header-container-inner, uni-nav-bar-text, uni-ellipsis-1, uni-navbar**header-btns-right, customimg, uni-navbar**placeholder, uni-navbar**placeholder-view, balance, txt, coin, cards, uni-list, uni-border-top-bottom, uni-list--border-top, my-list-item, my-list-item**container, container--right, slot-image, item, item-text, item-card, uni-icon-wrapper, uniui-arrowright, border--left, radio-list--border, radio-icon-wrapper, uni-radio-wrapper, uni-radio-input, uni-radio-input-checked, uni-list--border-bottom, amount-wrap, amount_txt, amount_input, currency, amount_coin, uni-input-wrapper, uni-input-placeholder, input-placeholder, uni-input-input, tips_amount, addbtn, btntips, my-tips-class, my-toptips, popup-tips, uni-mask, uni-actionsheet**mask, uni-actionsheet, uni-actionsheet**menu, uni-actionsheet**action, uni-actionsheet**cell, uni-modal, uni-modal**bd, uni-modal**ft, uni-modal**btn, uni-modal**btn_default, uni-modal**btn_primary
  - data-\* 属性：data-page, data-v-fe66a172, data-v-5725e711, data-v-dc21f774, data-v-90ccf07e, data-v-0a75b799, data-v-e07ee5ea, data-v-1b9ee996, data-v-1fdfd3f8, data-v-1ffe1b98
  - ID：无显式 id

- 布局：移动端单列，主要为 block 布局，列表项采用 flex 排版（图标 + 文本 + 箭头）

- 交互元素：
  - “Add New Bank Account” 卡片：跳转到添加银行账户页
  - 已验证账户卡片：选择/管理（存在删除图标）
  - 金额输入：number，步进控制
  - 提交按钮 WITHDRAW NOW：提交提现申请

- 响应式：主要针对移动端，桌面为窄宽容器

## 2. 功能流程分析

```mermaid
flowchart TD
  A[进入提现页] --> B[GET /draw/index?drawpop=0]
  B --> C[GET /user/banks]
  C --> D{是否已绑定账户}
  D -->|否| E[引导添加银行账户]
  D -->|是| F[选择目标账户]
  F --> G[输入金额]
  G --> H[点击 WITHDRAW NOW]
  H --> I[POST /draw/apply 或相关提交接口(未直接抓到)]
  I -->|成功| J[状态更新/跳转]
  I -->|失败| K[错误提示]
```

- 页面状态：
  - 初始渲染栏位由 /draw/index 和 /user/banks 返回数据驱动
  - 输入金额变更影响提交可用性
  - 提交后显示 loading、禁用（常见实践，UI未直接显示）

- 表单验证：
  - 金额 >= 最低限额，且 <= 最高限额
  - 必须选择或已存在一个有效银行账户
  - 错误方式：Toast/错误文本显示；当接口异常时显示“无法连接到服务器~”

- 导航与路由：
  - "Add New Bank Account" 跳转到 /pages/deposit/addcreditcard/addcreditcard
  - 可能支持返回

## 3. API 接口逆向工程

- 初始化：
  - GET http://service.haiwailaba.cyou/draw/index?drawpop=0
    - 用途：获取提现配置（限额、费率、提示文本等）
  - GET http://service.haiwailaba.cyou/user/banks
    - 用途：列出已绑定银行账户

- 提交：
  - 未直接抓到提交接口（当前界面仅展示，控制台有成功日志 {code:0, msg: succ, data:...} 来自 pages/withdraw/index.vue:135）
  - 常见命名：POST /draw/apply 或 /draw/submit（待进一步触发或登录后可见）

- 请求头：均为匿名请求，无 Authorization，常规浏览器头
- 错误响应：当网络失败或后端错误时，页面显示“无法连接到服务器~”

## 接口数据结构

### 1) GET /draw/index?drawpop=0

- JSON 示例（真实抓取，已脱敏精简）：

```
{
  "code":0,
  "msg":"succ",
  "data":{
    "uid":10002,
    "dcoin":1540,
    "limit":0,
    "mincoin":0,
    "maxcoin":1000000,
    "memo":"bbbbdddd<br/>awerqwerqwer",
    "popmsg":"",
    "customer":[],
    "url":"https://vm.providesupport.com/04qet..."
  }
}
```

- 字段说明：
  - dcoin:number 可提现余额 -> Withdrawable Balance
  - mincoin/maxcoin:number 限额提示 -> “Withdraw min :₹{min} & max:₹{max} allowed each time”
  - memo:string 提示文案（带<br/>） -> Tips 区域
  - limit:number/uid/customer/url/popmsg：控制弹窗、客服等业务开关

### 2) GET /user/banks

- JSON 示例（真实抓取，已脱敏精简）：

```
{
  "code":0,
  "msg":"succ",
  "data":{
    "banks":[{"id":4,"name":"VERIFIED","checked":true,"status":2,"card":"12************12","value":"4"}],
    "kyc":1,
    "url":"https://vm.providesupport.com/04qet..."
  }
}
```

- 字段说明：
  - banks:array 绑定的银行卡列表
    - name:string 显示状态（VERIFIED 等）
    - status:number 审核/验证状态（如 2=已验证）
    - checked:boolean 默认选择项
    - card:string 掩码卡号 -> “12\***\*\*\*\*\*\*\***12”
  - kyc:number 实名状态
- 数据驱动：
  - 若 banks 为空 -> 显示“Add New Bank Account”卡片为主，强调引导绑定
  - 若 banks 非空 -> 展示已验证卡片，并允许选择/管理

- 关键状态字段：
  - banks[].status/name/checked
  - dcoin/mincoin/maxcoin

## 4. 数据流分析

- 数据获取：
  - /draw/index 提供提现规则、提示语
  - /user/banks 提供银行卡列表

- 前端状态：
  - 选择的银行卡保存在内存状态
  - 输入金额为表单本地状态

- 刷新与缓存：
  - 刷新后需重新拉取 /draw/index 与 /user/banks

- 实时更新：
  - 未见 WebSocket，依赖请求刷新

## 克隆实现要点

- 组件：银行卡卡片列表、添加卡入口、金额输入与限额提示、提交按钮与状态提示
- API：按上述 GET 接口加载，提交接口占位并留可配置项
- 校验：金额区间、必选卡、失败兜底提示
- 交互：桌面/移动一致，触摸友好
