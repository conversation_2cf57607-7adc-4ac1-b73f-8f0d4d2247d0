# 交易记录页面分析（/pages/history/history）

目标： http://paysms.haiwailaba.cyou/#/pages/history/history

## 1. 页面结构分析

- 顶部：My Transactions 标题，返回、右侧图标
- 分类 Tab：Deposits / Withdrawals / Bet / Bonus
- 各分类下为时间分组的记录卡片列表，卡片包含：
  - 订单号 + Copy 按钮
  - 支付方式/类型（AliPay/upi 等）或图标
  - 时间（hh:mm am/pm）
  - 状态标签：Success / In-Process / Failed / Refund
  - 金额
  - 可选客服/反馈提示入口（“Need help with this order? Tap to contact us.”）
  - Refund 类型卡片附带 Reason:

- 主要 CSS 类/ID/data-\*（完整/节选）：
  - 类：uni-body, pages-transaction-transaction, uni-app--maxwidth, uni-page-refresh, uni-page-refresh-inner, uni-page-refresh**icon, uni-page-refresh**spinner, uni-page-refresh**path, uni-navbar, uni-dark, uni-nvue-fixed, uni-navbar**content, uni-navbar--fixed, uni-navbar--border, uni-status-bar, uni-navbar**header, uni-navbar**header-btns, uni-navbar**header-btns-left, uni-navbar**content_view, uni-icons, uniui-left, uni-navbar**header-container, uni-navbar**header-container-inner, uni-nav-bar-text, uni-ellipsis-1, uni-navbar**header-btns-right, customimg, uni-navbar**placeholder, uni-navbar**placeholder-view, scroll-view_H, uni-scroll-view, uni-scroll-view-content, scroll-view-item_H, activite, swiper-content, uni-swiper-wrapper, uni-swiper-slides, uni-swiper-slide-frame, swiper_item, filter-item, filter, filter-status, status-text, filter-icon, iconfont, icon-loudoushaixuan, mytips, payments, title, uni-list, uni-border-top-bottom, uni-list--border-top, my-list-item, my-list-item**container, container--right, item-icon, icon-shizhong, my-list-item**content, right_content, content_title, my-list-item**content-title, copy, my-list-item**content-note, coin, my-list-item**content-help, border--left, tran-list--border, help, uni-list--border-bottom, failed, icon-shibai, my-list-item**content-reason, icon-paysuccess, icon-transferout, icon-transferin, popup-tips, uni-mask, uni-actionsheet**mask, uni-actionsheet, uni-actionsheet**menu, uni-actionsheet**action, uni-actionsheet**cell, uni-modal, uni-modal**bd, uni-modal**ft, uni-modal**btn, uni-modal**btn_default, uni-modal**btn_primary
  - ID：equipment, expert, bet, bonus
  - data-\*：data-page, data-v-0bf322da, data-v-5725e711, data-v-dc21f774, data-v-90ccf07e, data-v-0a75b799, data-v-e07ee5ea, data-v-3e708e9c

- 布局：移动端列表，分组标题为日期（如 08/10/2025）

- 交互元素：
  - Tab 切换
  - 每条记录的 Copy、联系客服图标

## 2. 功能流程分析

```mermaid
flowchart TD
  A[进入交易记录页] --> B[GET /user/history?num=1&size=10]
  B --> C{切换 Tab}
  C -->|不同类型| D[附带筛选参数再次请求]
  D --> E[分页加载/下拉刷新]
```

- 页面状态：
  - 初始加载第一页（num=1&size=10）
  - 切换 Tab 可能改变查询参数（type/status）
  - 支持分页或滚动加载（未直接触发）

- 表单验证：无表单，主要是交互与展示

- 路由与导航：Hash 路由；可返回

## 3. API 接口逆向工程

- 列表接口：
  - GET http://service.haiwailaba.cyou/user/history?num=1&size=10
  - 请求头：匿名，无 Authorization
  - 响应：200 JSON（页面基于该数据渲染示例订单）

- 可能的筛选与分页：
  - num：页码
  - size：每页大小
  - 可能还有 type/status（未显式出现）

## 接口数据结构

### 1) GET /user/history?num={num}&size={size}

- JSON 示例（真实抓取，已脱敏精简）：

```
{
  "code":0,
  "msg":"succ",
  "data":{
    "deplist":{ "08/10/2025":[ {"id":54, "orderid":"2025...1975", "coin":"200.00", "status_str":"In-Process", "status":1, "time":"11:34 am", "title":"AliPay", "memo":"" }, ... ] },
    "drawlist":{ "08/07/2025":[ {"id":6, "orderid":"2025...5410", "coin":"10.00", "status_str":"In-Process", "status":0, "time":"09:11 am", "memo":"" }, {"status_str":"Refund","status":3,"memo":"asd"}, {"status_str":"Success","status":2} ] },
    "betlist":{ "08/07/2025":[ {"id":4, "orderid":"CS2508073185871968", "bet":230, "wincoin":0, "status":3, "gameid":12, "title":"Crash", "time":"07:40 am" }, ... ] },
    "bonuslist":{ "08/07/2025":[ {"id":25, "orderid":"2025...0293", "coin":"-30.00", "title":"签到彩金，第3天", "status":34, "time":"06:01 am", "status_str":"Transfer to Cash Balance" }, {"status_str":"Login bonus","coin":"30.00"}, ... ] },
    "banklist":[],
    "show":1,
    "url":"https://vm.providesupport.com/04qet..."
  }
}
```

- 字段说明：
  - data.deplist/drawlist/betlist/bonuslist：按日期字符串键分组的对象，每个键是数组
    - 通用字段：id:number, orderid:string, time:string, status:number, status_str:string
    - 分类字段：
      - deposit(充值)：coin:string 金额，title:string 支付方式（AliPay/upi）
      - draw(提现)：coin:string 金额，memo:string 退款原因等
      - bet(投注)：bet:number 投注额，wincoin:number|string 中奖额，gameid/title 游戏信息
      - bonus(红包/转账)：coin:string 正负金额，title:string 文案，status: 枚举（12=Lucky bonus, 34=Transfer to Cash Balance 等）
  - show:number 是否显示客服入口等开关
  - url:string 客服链接

- 数据驱动渲染：
  - Tab 内容：根据 deplist/drawlist/betlist/bonuslist 四类数据分别渲染
  - 分组标题：对象键名（日期，如 08/10/2025）
  - 卡片：
    - 标题/方式：title（AliPay/upi/游戏名/文案）
    - 订单号：orderid，附带 Copy
    - 时间：time（hh:mm am/pm）
    - 状态：status/status_str -> Success/In-Process/Failed/Refund/Transfer in/out 等
    - 金额：coin（string 保留两位）；betlist 使用 bet/wincoin
    - 附加说明：提现 Refund 需要显示 Reason: memo；充值“Need help...”依据 show/url

- 关键状态字段：
  - status/status_str：0=In-Process, 2=Success, 3=Refund/Failed 等（根据 status_str 判断展示风格）
  - show/url：是否展示客服与跳转地址

- 格式化与空/错态：
  - 金额：统一格式化为两位小数（若为字符串则直接显示）
  - 时间：前端按 hh:mm am/pm 展示；日期按 MM/DD/YYYY 分组标题
  - 空数据：某分类对象缺失或数组为空 -> 显示空态说明
  - 加载/错误：加载时骨架或spinner；请求失败显示错误提示

## 4. 数据流分析

- 获取与预处理：
  - 拉取历史数据，前端按日期分组并渲染
  - 金额格式化为两位小数，时间格式化为 hh:mm am/pm，日期为 MM/DD/YYYY

- 状态管理：
  - 当前 Tab、列表数据、分页游标

- 刷新与恢复：
  - 切换与刷新时重新请求；刷新后状态需重建

- 实时更新：
  - 未见 WebSocket；使用轮询或手动刷新

## 克隆实现要点

- 组件：Tabs、分组列表、记录卡片、复制按钮、客服入口
- API：/user/history，支持分页与筛选
- 体验：加载状态、空态、错误态、复制成功提示
