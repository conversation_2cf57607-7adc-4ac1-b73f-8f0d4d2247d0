# 充值页面分析（/pages/deposit/add）

本文基于对 http://paysms.haiwailaba.cyou/#/pages/deposit/add 页面在桌面视口的开发者工具检查与网络抓包结果整理，包含：页面结构、功能流程、API 逆向、数据流分析与克隆实现要点。

## 1. 页面结构分析

- 标题：Deposit
- 主体模块：
  - 顶部导航（返回图标、标题 Deposit、右侧图标）
  - 余额区块：
    - Current Balance 文本
    - 余额数值：₹6540.00
  - 金额输入区：
    - 左侧图标（uni-icons）
    - 输入框（默认值 200）
    - 右侧清除图标
  - 快捷金额列表：10、50、100、300、1000、3000、5000、10000
  - 支付方式（Payment Method）：
    - 方式项1：+500 / AliPay
    - 方式项2：+3.00% / upi
  - 支付通道（Payment Channel）：
    - 通道项：+500 / test1009
  - 限额提示：Deposit min :₹100 & max:₹1000000 allowed each time
  - 账户资金区：Cash Balance ₹200.00、Cash Bonus ₹500.00
  - 提交按钮：DEPOSIT NOW
  - Tips 文本："Tips: aaaa", "bbbbb", "ccccc"

- 主要 CSS 类名（完整/节选，UniApp/uni-ui 体系）：
  - 页面/容器：uni-body, pages-deposit-add-add, uni-app--maxwidth, content, uni-navbar, uni-dark, uni-nvue-fixed, uni-navbar**content, uni-navbar--fixed, uni-navbar--border, uni-status-bar, uni-navbar**header, uni-navbar**header-btns, uni-navbar**header-btns-left, uni-navbar**content_view, uni-navbar**header-container, uni-navbar**header-container-inner, uni-nav-bar-text, uni-ellipsis-1, uni-navbar**header-btns-right, customimg, uni-navbar**placeholder, uni-navbar**placeholder-view, balance, txt, coin, amount-wrap, amount, amount-list, amount-item, title, group-list, clicking, disrate, slot-image, grouptitle, item, channel-list, rowcoin, addbtn, btntips
  - 输入与图标：uni-easyinput, uni-easyinput**content, content-clear-icon, uniui-plusempty, uni-easyinput**content-input, uni-input-wrapper, uni-input-placeholder, uni-easyinput\_\_placeholder-class, uni-input-input, uni-icons, uniui-left, uniui-close
  - 弹层（可能用于选择或提示）：uni-mask, uni-actionsheet**mask, uni-actionsheet, uni-actionsheet**menu, uni-actionsheet**action, uni-actionsheet**cell, uni-modal, uni-modal**bd, uni-modal**ft, uni-modal**btn, uni-modal**btn_default, uni-modal\_\_btn_primary

- data-\* 属性：data-page, data-v-f9502768, data-v-5725e711, data-v-dc21f774, data-v-90ccf07e, data-v-0a75b799, data-v-75f84caa
- ID：无显式 id
- 布局方式：
  - 统计采样显示 display:block 为主，局部采用 flex（2 处）。整体是移动端单列布局，uni-app 组件化结构。

- 表单与交互元素：
  - 输入框：input[type=text]，默认值 200，无 placeholder、无 HTML 原生 required/pattern/min/max
  - 快捷金额按钮：点击应将金额注入输入框
  - 支付方式/通道项：选择时应记录选中项（如 AliPay/upi、test1009 等）
  - 提交按钮：DEPOSIT NOW，触发支付下单 API

- 响应式/移动端：
  - 页面以移动端（小程序/H5）为主的 uni-app 组件，样式与交互适配移动端；桌面视口可正常展示为居中窄屏。

## 2. 功能流程分析

- 操作流程（Mermaid）：

```mermaid
flowchart TD
  A[进入充值页] --> B[拉取余额 user/balance?cat=2&coin={默认或输入}&paypop=0]
  B --> C{选择金额}
  C -->|快捷金额/手输| D[选择支付方式]
  D --> E[选择支付通道]
  E --> F[点击 DEPOSIT NOW]
  F --> G[POST /order/pay]
  G -->|成功| H[跳转第三方/展示支付指引]
  G -->|失败| I[错误提示 + 重试]
```

- 页面状态变化：
  - 初始：从 localStorage 读取 amount（示例：{"type":"number","data":200}）并向 /user/balance 发起请求（含 coin=200）以回显余额与手续费等
  - 选择快捷金额：更新输入框数值；可能再次触发余额/费率刷新
  - 选择支付方式/通道：高亮选中项（class 切换）
  - 提交：按钮 loading/禁用（页面代码中不可见，但通常会有）

- 表单验证：
  - 客户端：
    - 金额应在 [100, 1000000] 区间（从提示可推测）
    - 金额为正数，可能要求整数
  - 触发时机：点击快捷金额、输入改变时进行本地校验；提交前再次校验
  - 错误提示：页面未见明确的错误提示容器；实际错误多由服务端返回并在 Toast/Modal 中提示

- 页面导航与路由：
  - Hash 路由：/#/pages/deposit/add
  - 该页可返回上一页；成功下单后通常跳转到第三方支付页或支付结果页（实际点击当前返回 500）
  - 状态保持：amount 被存入 localStorage；支付方式/通道选中状态可能保存在内存中

## 3. API 接口逆向工程

- 初始请求
  - GET http://service.haiwailaba.cyou/user/balance?cat=2&coin=200&paypop=0
  - 请求头：未见 Authorization；常规浏览器头；Content-Type 为空（GET）
  - 响应：200 JSON（未详细展开，页面成功渲染余额 ₹6540.00）

- 提交下单
  - POST http://service.haiwailaba.cyou/order/pay
  - 请求头：content-type: application/x-www-form-urlencoded
  - 请求体（示例）：amount=200&id=10
    - amount：充值金额（来源输入）
    - id：推断为所选支付通道/方式 ID（当前 UI 有 Payment Method 和 Payment Channel，多数项目将最终选择映射为一个 ID）

- 其他资源：
  - 静态资源加载正常
  - 本地存储：localStorage.\_\_DC_STAT_UUID, localStorage.amount

- 认证机制：
  - 访问无需登录 Cookie；API 未见 Authorization 头
  - 推断该 Demo 环境为公开测试或通过 IP 识别/服务端白名单

- 查询参数：
  - /user/balance: cat=2, coin={金额}, paypop=0

- 时序与依赖：
  - 先请求余额/费率等（user/balance），后在提交时调用 order/pay

## 接口数据结构

### 1) GET /user/balance?cat=2&coin=&paypop=0

- JSON 示例（真实抓取，已脱敏精简）：

```
{
  "code": 0,
  "msg": "succ",
  "data": {
    "user": {
      "uid": 10002, "coin": 6540, "dcoin": 1540, "ecoin": 5000,
      "bonus": 2450, "totalcoin": 6540, "kyc": 1, "svip": 4,
      "ispayer": 1, "nodislabelid": 0
    },
    "coins": [10,50,100,300,1000,3000,5000,10000],
    "channels": [
      {
        "id": 10, "title": "AliPay", "icon": "https://...png",
        "subtype": "onlinepay", "mincoin": 100, "maxcoin": 1000000,
        "disrate": "+500", "discoin": 500, "type": 0,
        "pages": [
          {"id": 10, "title": "test1009", "type": 1, "banktype": 0,
           "mincoin": 100, "maxcoin": 1000000, "disrate": "+500",
           "discoin": 500, "rate": "2.50"}
        ]
      },
      {
        "id": 17, "title": "upi", "icon": "https://img.yonogames.com/",
        "subtype": "onlinepay", "mincoin": 200, "maxcoin": 100000,
        "disrate": "+3.00%", "discoin": 0, "type": 0,
        "pages": [
          {"id": 16, "title": "Paytm APP", "type": 1, "banktype": 0,
           "mincoin": 200, "maxcoin": 100000, "disrate": "+3.00%",
           "discoin": 0, "rate": 0.03}
        ]
      }
    ],
    "memo": "Tips: aaaa<br/>\nbbbbb<br/>\nccccc",
    "popmsg": "", "customer": [],
    "url": "https://vm.providesupport.com/04qet..."
  }
}
```

- 字段说明：
  - code:number 响应码，0=成功
  - msg:string 文本消息
  - data.user:object 用户与余额信息
    - uid:number 用户ID
    - coin:number 当前账户余额（用于“Current Balance”）
    - dcoin:number 可提现余额
    - ecoin:number 可能为现金余额或其他余额（页面未直接展示）
    - bonus:number 红利余额（页面可能用于“Cash Bonus”展示）
    - totalcoin:number 总余额
    - kyc:number 0/1 实名状态
    - svip:number、ispayer:number、nodislabelid:number 业务开关/等级
  - data.coins:number[] 快捷金额按钮列表
  - data.channels:array 支付方式与其下页面/通道
    - id:number 支付方式ID（提交 /order/pay 时使用，见下）
    - title:string 支付方式名（AliPay/upi）
    - disrate:string 展示用折扣/加送（如“+3.00%”/“+500”）
    - mincoin/maxcoin:number 限额
    - pages:array 具体支付通道页（如 test1009、Paytm APP）
      - pages[].id:number 通道页ID（前端未直接用作提交ID）
      - rate:number|string 手续费或费率
  - data.memo:string 带 `<br/>` 的提示文案（UI转为多行）
  - data.url:string 客服地址

- 数据驱动渲染：
  - Current Balance = data.user.coin
  - 快捷金额 = data.coins
  - Payment Method 列表 = data.channels[*].title + disrate
  - Payment Channel 列表 = 所选方式下 data.channels[i].pages[*].title
  - 限额提示 = 使用所选方式/通道的 mincoin/maxcoin
  - Tips = data.memo（将 `<br/>` 转换为换行）
  - “Cash Balance/Bonus”区域：与 user.ecoin/user.bonus 相关（当前环境显示为示例值，实现时以接口为准）

- 关键状态字段：
  - user.kyc（认证）、channels[].mincoin/maxcoin（限额）、pages[].rate（费率）

### 2) POST /order/pay（提交）

- 请求体（真实抓取）：content-type: application/x-www-form-urlencoded
  - amount={金额}&id={支付方式ID}
  - 示例：amount=200&id=10

- 渲染与状态：
  - 成功时（预期）：应返回包含跳转链接或支付指引的数据，前端根据结果跳转/展示
  - 失败/异常：展示错误Toast；避免对返回体未判空即做字符串操作

- 字段说明表（用户与余额）：

| 字段      | 类型   | 含义                | UI 映射                            |
| --------- | ------ | ------------------- | ---------------------------------- |
| uid       | number | 用户ID              | -                                  |
| coin      | number | 当前账户余额        | Current Balance                    |
| dcoin     | number | 可提现余额          | 提现页 Withdrawable Balance        |
| ecoin     | number | 现金/主余额（推测） | Cash Balance（实现时以接口域为准） |
| bonus     | number | 红利余额            | Cash Bonus（实现时以接口域为准）   |
| totalcoin | number | 总余额              | -                                  |
| kyc       | 0/1    | 实名状态            | 影响认证流程/标识                  |
| svip      | number | 等级（业务）        | -                                  |
| ispayer   | number | 支付开关（业务）    | -                                  |

- 字段说明表（支付方式/通道）：

| 字段                       | 类型   | 含义          | 备注                   |                |
| -------------------------- | ------ | ------------- | ---------------------- | -------------- |
| channels[].id              | number | 支付方式ID    | /order/pay 的 id       |                |
| channels[].title           | string | 方式名        | AliPay/upi             |                |
| channels[].mincoin/maxcoin | number | 限额          | 用于限额提示           |                |
| channels[].disrate         | string | 展示加送/费率 | “+500”/“+3.00%”        |                |
| channels[].discoin         | number | 加送金额      | 与 disrate 对应        |                |
| channels[].pages[].id      | number | 通道页ID      | 前端通常不直接提交此ID |                |
| channels[].pages[].title   | string | 通道名        | test1009/Paytm APP     |                |
| channels[].pages[].rate    | number | string        | 费率                   | 0.03 或 "2.50" |

- 展示格式化规则：
  - 金额：前端统一格式化为“₹{amount.toFixed(2)}”
  - 文案：memo 内的 `<br/>` 转换为换行

## 4. 数据流分析

- 前端数据处理：
  - 初始化从 localStorage 读取 amount
  - GET /user/balance 返回用于展示余额、费率、限额和可用支付方式/通道
  - 选择与输入驱动金额与费率显示变化

- 校验与转换：
  - 将输入金额转为数字，确保在限额区间；POST 时以表单 URL 编码提交

- 状态管理：
  - 本地状态（内存 + localStorage.amount）
  - 页面刷新后保留上次金额值

- 实时更新：
  - 未见 WebSocket；关键数据依靠请求刷新

## 克隆实现要点

- 组件：
  - 金额输入组件 + 快捷金额按钮组
  - 支付方式 + 支付通道选择器
  - 余额/费用展示卡片
  - 提交按钮与 Toast/Modal 提示

- API：
  - GET /user/balance?cat=2&coin={amount}&paypop=0
  - POST /order/pay (application/x-www-form-urlencoded)

- 校验：
  - 提交前校验金额区间、必选支付方式与通道

- 路由与状态：
  - 刷新保持金额（localStorage），其余状态放在内存或 URL 查询参数
