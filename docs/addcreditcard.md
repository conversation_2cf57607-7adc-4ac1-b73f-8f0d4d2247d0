# 添加银行账户页面分析（/pages/deposit/addcreditcard/addcreditcard）

目标： http://paysms.haiwailaba.cyou/#/pages/deposit/addcreditcard/addcreditcard

## 1. 页面结构分析

- 标题：Add New Bank Account
- 表单字段：
  - Account Number（账户号）
    - 输入：spinbutton（数字），placeholder: Enter your Bank Account Number
  - Retype Account Number（确认账户号）
    - 输入：spinbutton（数字），placeholder: Confirm your Bank Account Number
  - IFSC Code
    - 输入：textbox（文本），placeholder: Enter 11-digit Bank IFSC Code
  - Name（姓名）
    - 输入：textbox disabled，值 Test（来自接口 /kyc/name）
  - BANK PROOF（银行证明）
    - 上传区：2 个占位图 + 上传图标按钮
  - IMPORTANT（重要提示）
    - 列表：
      - Review your details before submitting your documents permanently.
      - Bank account once added can't be changed later.
  - 提交按钮：SUBMIT DETAILS
  - 错误提示：无法连接到服务器~（当接口失败）

- 类名/属性（完整/节选）：
  - 类：uni-body, pages-deposit-addcreditcard-addcreditcard, uni-app--maxwidth, content, uni-navbar, uni-dark, uni-nvue-fixed, uni-navbar**content, uni-navbar--fixed, uni-navbar--border, uni-status-bar, uni-navbar**header, uni-navbar**header-btns, uni-navbar**header-btns-left, uni-navbar**content_view, uni-icons, uniui-left, uni-navbar**header-container, uni-navbar**header-container-inner, uni-nav-bar-text, uni-ellipsis-1, uni-navbar**header-btns-right, customimg, uni-navbar**placeholder, uni-navbar**placeholder-view, title, balance, txt, uni-input, uni-input-wrapper, uni-input-placeholder, input-placeholder, uni-input-input, form, uploadImg, uploadBtn, my-container, my-upload-box, my-upload-add, my-upload-icon, my-icon-plus, rule, sub-title, detail, amount-wrap, addbtn, my-tips-class, my-toptips, popup-tips, uni-mask, uni-actionsheet**mask, uni-actionsheet, uni-actionsheet**menu, uni-actionsheet**action, uni-actionsheet**cell, uni-modal, uni-modal**bd, uni-modal**ft, uni-modal**btn, uni-modal**btn_default, uni-modal\_\_btn_primary
  - data-\*：data-page, data-v-3eb73ab6, data-v-5725e711, data-v-dc21f774, data-v-90ccf07e, data-v-0a75b799, data-v-43a3f841, data-v-1ffe1b98
  - ID：无

- 布局：表单单列布局，移动端适配良好

- 表单校验：
  - 账户号必填、两次一致、数字
  - IFSC 长度 11、格式（字母数字组合）
  - 必须上传银行证明

## 2. 功能流程分析

```mermaid
flowchart TD
  A[进入页面] --> B[GET /kyc/name?cat=bank&bankpop=0]
  B --> C[自动回填 Name]
  C --> D[填写账户号/IFSC + 上传证明]
  D --> E[点击 SUBMIT DETAILS]
  E --> F[POST /kyc/bank 或类似接口(未抓到)]
  F -->|成功| G[进入审核/返回管理页]
  F -->|失败| H[错误提示]
```

- 验证触发：blur/submit 时；错误通过 Toast/红字提示
- 导航：提交成功后返回管理页或进入审核进度页

## 3. API 接口逆向工程

- 进入页：
  - GET http://service.haiwailaba.cyou/kyc/name?cat=bank&bankpop=0
    - 返回用户姓名用于回填 Name 字段

- 提交：
  - 未抓到，可能为 multipart/form-data 上传（含两张图）+ 表单字段

- 请求头：匿名

## 接口数据结构

### 1) GET /kyc/name?cat=bank&bankpop=0

- JSON 示例（真实抓取，已脱敏精简）：

```
{
  "code":0,
  "msg":"succ",
  "data":{
    "item": {"name":"Test"},
    "popmsg":"",
    "url":"https://vm.providesupport.com/04qet..."
  }
}
```

- 字段说明：
  - data.item.name:string 用户姓名 -> 回填 Name 字段（disabled）
  - popmsg/url：弹窗/客服相关

- 数据驱动渲染：
  - Name 直接展示 data.item.name
  - 其他表单字段由用户输入；提交时需携带账户号、确认账户号、IFSC、两张 BANK PROOF 图片

### 2) POST /kyc/bank（推测）

- Body: multipart/form-data
  - 字段：account, account_confirm, ifsc, name, proofs[] 等
- 响应（推测）：code/msg/data（审核状态）
- 状态与条件渲染：
  - 成功 -> 跳转回管理页/进入审核中状态
  - 失败 -> 提示错误（字段级或全局）

## 4. 数据流分析

- 数据获取：姓名回填
- 本地状态：表单字段、上传文件列表
- 刷新后：需重新拉取姓名、用户需重新上传
- 实时：无

## 克隆实现要点

- 组件：表单输入、双重确认、IFSC 校验、上传组件（预览/删除）
- API：GET /kyc/name；POST /kyc/bank（占位）
- 容错：接口失败的兜底文案与禁用状态
- 校验：前后端一致校验，防止提交非法数据
